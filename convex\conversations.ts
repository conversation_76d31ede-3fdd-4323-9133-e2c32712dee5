import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Create a private conversation
export const createPrivateConversation = mutation({
  args: {
    userId1: v.id("users"),
    userId2: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Check if conversation already exists
    const existingConversations = await ctx.db
      .query("conversationMembers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId1))
      .collect();

    for (const membership of existingConversations) {
      const conversation = await ctx.db.get(membership.conversationId);
      if (conversation?.type === "private") {
        const otherMember = await ctx.db
          .query("conversationMembers")
          .withIndex("by_conversation", (q) => q.eq("conversationId", membership.conversationId))
          .filter((q) => q.neq(q.field("userId"), args.userId1))
          .first();

        if (otherMember?.userId === args.userId2) {
          return membership.conversationId;
        }
      }
    }

    // Create new conversation
    const conversationId = await ctx.db.insert("conversations", {
      type: "private",
      createdBy: args.userId1,
      createdAt: Date.now(),
      isActive: true,
      lastMessageAt: Date.now(),
    });

    // Add both users as members
    await ctx.db.insert("conversationMembers", {
      conversationId,
      userId: args.userId1,
      role: "member",
      joinedAt: Date.now(),
      lastReadAt: Date.now(),
    });

    await ctx.db.insert("conversationMembers", {
      conversationId,
      userId: args.userId2,
      role: "member",
      joinedAt: Date.now(),
      lastReadAt: Date.now(),
    });

    return conversationId;
  },
});

// Create a group chat
export const createGroupChat = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    createdBy: v.id("users"),
    memberIds: v.array(v.id("users")),
  },
  handler: async (ctx, args) => {
    const conversationId = await ctx.db.insert("conversations", {
      type: "group",
      name: args.name,
      description: args.description,
      createdBy: args.createdBy,
      createdAt: Date.now(),
      isActive: true,
      lastMessageAt: Date.now(),
    });

    // Add creator as admin
    await ctx.db.insert("conversationMembers", {
      conversationId,
      userId: args.createdBy,
      role: "admin",
      joinedAt: Date.now(),
      lastReadAt: Date.now(),
    });

    // Add other members
    for (const memberId of args.memberIds) {
      if (memberId !== args.createdBy) {
        await ctx.db.insert("conversationMembers", {
          conversationId,
          userId: memberId,
          role: "member",
          joinedAt: Date.now(),
          lastReadAt: Date.now(),
        });
      }
    }

    // Send welcome message
    const creator = await ctx.db.get(args.createdBy);
    await ctx.db.insert("messages", {
      conversationId,
      senderId: args.createdBy,
      content: `${creator?.nickname || creator?.username} created the group "${args.name}"`,
      type: "system",
      timestamp: Date.now(),
    });

    return conversationId;
  },
});

// Create a public room
export const createRoom = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    category: v.string(),
    createdBy: v.id("users"),
    maxMembers: v.optional(v.float64()),
    rules: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const conversationId = await ctx.db.insert("conversations", {
      type: "room",
      name: args.name,
      description: args.description,
      category: args.category,
      createdBy: args.createdBy,
      createdAt: Date.now(),
      isActive: true,
      isPublic: true,
      maxMembers: args.maxMembers || 100,
      rules: args.rules,
      lastMessageAt: Date.now(),
    });

    // Add creator as admin
    await ctx.db.insert("conversationMembers", {
      conversationId,
      userId: args.createdBy,
      role: "admin",
      joinedAt: Date.now(),
      lastReadAt: Date.now(),
    });

    // Send welcome message
    const creator = await ctx.db.get(args.createdBy);
    await ctx.db.insert("messages", {
      conversationId,
      senderId: args.createdBy,
      content: `Welcome to ${args.name}! 🇿🇦 ${args.description || 'Let\'s chat and have a lekker time!'}`,
      type: "system",
      timestamp: Date.now(),
    });

    return conversationId;
  },
});

// Get user's conversations
export const getUserConversations = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const memberships = await ctx.db
      .query("conversationMembers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    const conversations = await Promise.all(
      memberships.map(async (membership) => {
        const conversation = await ctx.db.get(membership.conversationId);
        if (!conversation) return null;

        // Get last message
        const lastMessage = await ctx.db
          .query("messages")
          .withIndex("by_conversation_timestamp", (q) => 
            q.eq("conversationId", membership.conversationId)
          )
          .order("desc")
          .first();

        // Count unread messages
        const unreadMessages = await ctx.db
          .query("messages")
          .withIndex("by_conversation_timestamp", (q) => 
            q.eq("conversationId", membership.conversationId)
          )
          .filter((q) => 
            q.and(
              q.gt(q.field("timestamp"), membership.lastReadAt || 0),
              q.neq(q.field("senderId"), args.userId)
            )
          )
          .collect();

        // Get conversation display info
        let displayName = conversation.name;
        let displayAvatar = conversation.avatar;
        let otherUser = null;

        if (conversation.type === "private") {
          // For private chats, get the other user's info
          const otherMember = await ctx.db
            .query("conversationMembers")
            .withIndex("by_conversation", (q) => q.eq("conversationId", membership.conversationId))
            .filter((q) => q.neq(q.field("userId"), args.userId))
            .first();

          if (otherMember) {
            otherUser = await ctx.db.get(otherMember.userId);
            displayName = membership.nickname || otherUser?.nickname || otherUser?.username || "Unknown";
            displayAvatar = otherUser?.avatar;
          }
        }

        // Get member count for groups/rooms
        const memberCount = await ctx.db
          .query("conversationMembers")
          .withIndex("by_conversation", (q) => q.eq("conversationId", membership.conversationId))
          .collect();

        return {
          _id: conversation._id,
          type: conversation.type,
          name: displayName,
          description: conversation.description,
          avatar: displayAvatar,
          category: conversation.category,
          lastMessage: lastMessage ? {
            content: lastMessage.content,
            timestamp: lastMessage.timestamp,
            senderId: lastMessage.senderId,
            type: lastMessage.type,
          } : null,
          unreadCount: unreadMessages.length,
          memberCount: memberCount.length,
          lastReadAt: membership.lastReadAt,
          role: membership.role,
          otherUser: otherUser ? {
            _id: otherUser._id,
            username: otherUser.username,
            nickname: otherUser.nickname,
            avatar: otherUser.avatar,
            status: otherUser.status,
            mood: otherUser.mood,
          } : null,
        };
      })
    );

    return conversations
      .filter(Boolean)
      .sort((a, b) => {
        const aTime = a?.lastMessage?.timestamp || 0;
        const bTime = b?.lastMessage?.timestamp || 0; 
        return bTime - aTime;
      });
  },
});

// Get public rooms by category
export const getPublicRooms = query({
  args: { 
    category: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("conversations")
      .filter((q) => 
        q.and(
          q.eq(q.field("type"), "room"),
          q.eq(q.field("isPublic"), true),
          q.eq(q.field("isActive"), true)
        )
      );

    if (args.category) {
      query = query.filter((q) => q.eq(q.field("category"), args.category));
    }

    const rooms = await query.take(args.limit || 20);

    const roomsWithInfo = await Promise.all(
      rooms.map(async (room) => {
        const memberCount = await ctx.db
          .query("conversationMembers")
          .withIndex("by_conversation", (q) => q.eq("conversationId", room._id))
          .collect();

        const creator = await ctx.db.get(room.createdBy);

        const lastMessage = await ctx.db
          .query("messages")
          .withIndex("by_conversation_timestamp", (q) => 
            q.eq("conversationId", room._id)
          )
          .order("desc")
          .first();

        return {
          ...room,
          memberCount: memberCount.length,
          creator: creator ? {
            username: creator.username,
            nickname: creator.nickname,
          } : null,
          lastActivity: lastMessage?.timestamp || room.createdAt,
        };
      })
    );

    return roomsWithInfo.sort((a, b) => b.lastActivity - a.lastActivity);
  },
});

// Join a room
export const joinRoom = mutation({
  args: {
    roomId: v.id("conversations"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const room = await ctx.db.get(args.roomId);
    if (!room || room.type !== "room") {
      throw new Error("Room not found");
    }

    // Check if already a member
    const existingMembership = await ctx.db
      .query("conversationMembers")
      .withIndex("by_conversation_user", (q) => 
        q.eq("conversationId", args.roomId).eq("userId", args.userId)
      )
      .first();

    if (existingMembership) {
      return args.roomId;
    }

    // Check room capacity
    const currentMembers = await ctx.db
      .query("conversationMembers")
      .withIndex("by_conversation", (q) => q.eq("conversationId", args.roomId))
      .collect();

    if (room.maxMembers && currentMembers.length >= room.maxMembers) {
      throw new Error("Room is full");
    }

    // Add user to room
    await ctx.db.insert("conversationMembers", {
      conversationId: args.roomId,
      userId: args.userId,
      role: "member",
      joinedAt: Date.now(),
      lastReadAt: Date.now(),
    });

    // Send join message
    const user = await ctx.db.get(args.userId);
    await ctx.db.insert("messages", {
      conversationId: args.roomId,
      senderId: args.userId,
      content: `${user?.nickname || user?.username} joined the room! 👋`,
      type: "system",
      timestamp: Date.now(),
    });

    return args.roomId;
  },
});

// Leave a conversation
export const leaveConversation = mutation({
  args: {
    conversationId: v.id("conversations"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const membership = await ctx.db
      .query("conversationMembers")
      .withIndex("by_conversation_user", (q) => 
        q.eq("conversationId", args.conversationId).eq("userId", args.userId)
      )
      .first();

    if (!membership) {
      throw new Error("User is not a member of this conversation");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation) {
      throw new Error("Conversation not found");
    }

    // Can't leave private conversations
    if (conversation.type === "private") {
      throw new Error("Cannot leave private conversations");
    }

    // Remove membership
    await ctx.db.delete(membership._id);

    // Send leave message
    const user = await ctx.db.get(args.userId);
    await ctx.db.insert("messages", {
      conversationId: args.conversationId,
      senderId: args.userId,
      content: `${user?.nickname || user?.username} left the ${conversation.type}`,
      type: "system",
      timestamp: Date.now(),
    });
  },
});
