import { GenericId, v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Send a message
export const sendMessage = mutation({
  args: {
    conversationId: v.id("conversations"),
    senderId: v.id("users"),
    content: v.string(),
    type: v.optional(v.union(
      v.literal("text"), 
      v.literal("image"), 
      v.literal("file"), 
      v.literal("voice"),
      v.literal("sticker")
    )),
    replyToId: v.optional(v.id("messages")),
    fileUrl: v.optional(v.string()),
    fileName: v.optional(v.string()),
    fileSize: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Verify user is member of conversation
    const membership = await ctx.db
      .query("conversationMembers")
      .withIndex("by_conversation_user", (q) => 
        q.eq("conversationId", args.conversationId).eq("userId", args.senderId)
      )
      .first();

    if (!membership) {
      throw new Error("User is not a member of this conversation");
    }

    // Process Mxit-style emoticons in text messages
    let processedContent = args.content;
    if (args.type === "text" || !args.type) {
      processedContent = convertMxitEmoticons(args.content);
    }

    // Create message
    const messageId = await ctx.db.insert("messages", {
      conversationId: args.conversationId,
      senderId: args.senderId,
      content: processedContent,
      type: args.type || "text",
      timestamp: Date.now(),
      replyToId: args.replyToId,
      fileUrl: args.fileUrl,
      fileName: args.fileName,
      fileSize: args.fileSize,
    });

    // Update conversation's last message time
    await ctx.db.patch(args.conversationId, {
      lastMessageAt: Date.now(),
    });

    // Update sender's last read time
    await ctx.db.patch(membership._id, {
      lastReadAt: Date.now(),
    });

    return messageId;
  },
});

// Get messages for a conversation
export const getMessages = query({
  args: { 
    conversationId: v.id("conversations"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_conversation_timestamp", (q) => 
        q.eq("conversationId", args.conversationId)
      )
      .order("desc")
      .take(args.limit || 50);

    // Get sender info for each message
    const messagesWithSenders = await Promise.all(
      messages.reverse().map(async (message) => {
        const sender = await ctx.db.get(message.senderId);
        const replyTo = message.replyToId ? await ctx.db.get(message.replyToId) : null;
        
        return {
          ...message,
          sender: sender ? {
            _id: sender._id,
            username: sender.username,
            nickname: sender.nickname,
            avatar: sender.avatar,
          } : null,
          replyTo: replyTo ? {
            ...replyTo,
            sender: await ctx.db.get(replyTo.senderId),
          } : null,
        };
      })
    );

    return messagesWithSenders;
  },
});

// Mark messages as read
export const markAsRead = mutation({
  args: {
    conversationId: v.id("conversations"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const membership = await ctx.db
      .query("conversationMembers")
      .withIndex("by_conversation_user", (q) => 
        q.eq("conversationId", args.conversationId).eq("userId", args.userId)
      )
      .first();

    if (membership) {
      await ctx.db.patch(membership._id, {
        lastReadAt: Date.now(),
      });
    }
  },
});

// Add reaction to message
export const addReaction = mutation({
  args: {
    messageId: v.id("messages"),
    userId: v.id("users"),
    emoji: v.string(),
  },
  handler: async (ctx, args) => {
    const message = await ctx.db.get(args.messageId);
    if (!message) throw new Error("Message not found");

    const reactions = message.reactions || [];
    
    // Remove existing reaction from this user
    const filteredReactions = reactions.filter((r: { userId: GenericId<"users">; }) => r.userId !== args.userId);
    
    // Add new reaction
    filteredReactions.push({
      emoji: args.emoji,
      userId: args.userId,
      timestamp: Date.now(),
    });

    await ctx.db.patch(args.messageId, {
      reactions: filteredReactions,
    });
  },
});

// Delete message
export const deleteMessage = mutation({
  args: {
    messageId: v.id("messages"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const message = await ctx.db.get(args.messageId);
    if (!message) throw new Error("Message not found");

    // Only sender can delete their own messages
    if (message.senderId !== args.userId) {
      throw new Error("You can only delete your own messages");
    }

    await ctx.db.patch(args.messageId, {
      isDeleted: true,
      content: "This message was deleted",
    });
  },
});

// Edit message
export const editMessage = mutation({
  args: {
    messageId: v.id("messages"),
    userId: v.id("users"),
    newContent: v.string(),
  },
  handler: async (ctx, args) => {
    const message = await ctx.db.get(args.messageId);
    if (!message) throw new Error("Message not found");

    // Only sender can edit their own messages
    if (message.senderId !== args.userId) {
      throw new Error("You can only edit your own messages");
    }

    // Can only edit text messages
    if (message.type !== "text") {
      throw new Error("You can only edit text messages");
    }

    const processedContent = convertMxitEmoticons(args.newContent);

    await ctx.db.patch(args.messageId, {
      content: processedContent,
      editedAt: Date.now(),
    });
  },
});

// Helper function to convert Mxit-style emoticons
function convertMxitEmoticons(text: string): string {
  const emoticons: Record<string, string> = {
    ':)': '😊',
    ':-)': '😊',
    ':(': '😢',
    ':-(': '😢',
    ':D': '😃',
    ':-D': '😃',
    ':P': '😛',
    ':-P': '😛',
    ';)': '😉',
    ';-)': '😉',
    ':o': '😮',
    ':-o': '😮',
    ':*': '😘',
    ':-*': '😘',
    '<3': '❤️',
    '</3': '💔',
    ':@': '😠',
    ':-@': '😠',
    '8)': '😎',
    '8-)': '😎',
    ':S': '😕',
    ':-S': '😕',
    ':$': '😳',
    ':-$': '😳',
    // SA-specific additions
    'braai': '🍖',
    'boerewors': '🌭',
    'biltong': '🥩',
    'rugby': '🏉',
    'eish': '😅',
    'howzit': '👋',
    'lekker': '👌',
    'sharp': '👍',
    'ja': '✅',
    'nee': '❌',
  };

  let result = text;
  Object.entries(emoticons).forEach(([emoticon, emoji]) => {
    const regex = new RegExp(escapeRegExp(emoticon), 'gi');
    result = result.replace(regex, emoji);
  });

  return result;
}

function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
