import { api } from "./_generated/api";
import { mutation } from "./_generated/server";

// Migration to fix existing conversations that are missing required fields
export const fixConversations = mutation({
  args: {},
  handler: async (ctx) => {
    const conversations = await ctx.db.query("conversations").collect();
    
    for (const conversation of conversations) {
      const updates: any = {};
      
      // Add missing createdAt field
      if (!conversation.createdAt) {
        updates.createdAt = Date.now();
      }
      
      // Add missing lastMessageAt field
      if (!conversation.lastMessageAt) {
        updates.lastMessageAt = Date.now();
      }
      
      // Add missing isActive field
      if (conversation.isActive === undefined) {
        updates.isActive = true;
      }
      
      // Update if there are any missing fields
      if (Object.keys(updates).length > 0) {
        await ctx.db.patch(conversation._id, updates);
      }
    }
    
    return { fixed: conversations.length };
  },
});

// Migration to fix existing conversation members
export const fixConversationMembers = mutation({
  args: {},
  handler: async (ctx) => {
    const members = await ctx.db.query("conversationMembers").collect();
    
    for (const member of members) {
      const updates: any = {};
      
      // Add missing joinedAt field
      if (!member.joinedAt) {
        updates.joinedAt = Date.now();
      }
      
      // Update if there are any missing fields
      if (Object.keys(updates).length > 0) {
        await ctx.db.patch(member._id, updates);
      }
    }
    
    return { fixed: members.length };
  },
});

// Migration to fix existing messages
export const fixMessages = mutation({
  args: {},
  handler: async (ctx) => {
    const messages = await ctx.db.query("messages").collect();
    
    for (const message of messages) {
      const updates: any = {};
      
      // Add missing timestamp field
      if (!message.timestamp) {
        updates.timestamp = Date.now();
      }
      
      // Update if there are any missing fields
      if (Object.keys(updates).length > 0) {
        await ctx.db.patch(message._id, updates);
      }
    }
    
    return { fixed: messages.length };
  },
});

// Clean up invalid conversations
export const cleanupInvalidConversations = mutation({
  args: {},
  handler: async (ctx) => {
    const conversations = await ctx.db.query("conversations").collect();
    let deletedCount = 0;
    
    for (const conversation of conversations) {
      // Check if conversation has required fields
      if (!conversation.createdBy || !conversation.type) {
        // Delete invalid conversation and its members/messages
        await ctx.db.delete(conversation._id);
        
        // Delete associated members
        const members = await ctx.db
          .query("conversationMembers")
          .withIndex("by_conversation", (q) => q.eq("conversationId", conversation._id))
          .collect();
        
        for (const member of members) {
          await ctx.db.delete(member._id);
        }
        
        // Delete associated messages
        const messages = await ctx.db
          .query("messages")
          .withIndex("by_conversation", (q) => q.eq("conversationId", conversation._id))
          .collect();
        
        for (const message of messages) {
          await ctx.db.delete(message._id);
        }
        
        deletedCount++;
      }
    }
    
    return { deleted: deletedCount };
  },
});

// Run all migrations
export const runAllMigrations = mutation({
  args: {},
  handler: async (ctx): Promise<{
    cleanup: any; // Replace 'any' with the actual return type
    conversations: any;
    members: any;
    messages: any;
  }> => {
    const cleanupResult = await ctx.runMutation(api.migrations.cleanupInvalidConversations, {});
    const conversationsResult = await ctx.runMutation(api.migrations.fixConversations, {});
    const membersResult = await ctx.runMutation(api.migrations.fixConversationMembers, {});
    const messagesResult = await ctx.runMutation(api.migrations.fixMessages, {});
    
    return {
      cleanup: cleanupResult,
      conversations: conversationsResult,
      members: membersResult,
      messages: messagesResult,
    };
  },
});