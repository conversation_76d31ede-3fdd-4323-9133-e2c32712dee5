import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Users table with Mxit-style profiles
  users: defineTable({
    clerkId: v.string(),
    email: v.string(),
    username: v.string(),
    nickname: v.optional(v.string()),
    avatar: v.optional(v.string()),
    mood: v.optional(v.string()),
    status: v.union(v.literal("online"), v.literal("away"), v.literal("busy"), v.literal("offline")),
    location: v.optional(v.string()), // SA province/city
    language: v.optional(v.string()), // Afrikaans, Zulu, Xhosa, etc.
    joinedAt: v.number(),
    lastSeen: v.number(),
    isVerified: v.optional(v.boolean()),
    // Mxit-style profile fields
    aboutMe: v.optional(v.string()),
    favoriteEmoji: v.optional(v.string()),
    braaiScore: v.optional(v.number()), // Fun SA metric
  })
    .index("by_clerk_id", ["clerkId"])
    .index("by_username", ["username"])
    .index("by_status", ["status"]),

  // Conversations (private chats and group chats)
  conversations: defineTable({
    type: v.union(v.literal("private"), v.literal("group"), v.literal("room")),
    name: v.optional(v.string()), // For group chats and rooms
    description: v.optional(v.string()),
    avatar: v.optional(v.string()),
    createdBy: v.id("users"),
    createdAt: v.float64(),
    lastMessageAt: v.optional(v.float64()),
    isActive: v.boolean(),
    // Room-specific fields
    isPublic: v.optional(v.boolean()),
    category: v.optional(v.string()), // "general", "sports", "music", "braai", etc.
    maxMembers: v.optional(v.float64()),
    rules: v.optional(v.string()),
  })
    .index("by_type", ["type"])
    .index("by_category", ["category"])
    .index("by_created_by", ["createdBy"]),

  // Conversation members
  conversationMembers: defineTable({
    conversationId: v.id("conversations"),
    userId: v.id("users"),
    role: v.union(v.literal("admin"), v.literal("moderator"), v.literal("member")),
    joinedAt: v.float64(),
    lastReadAt: v.optional(v.float64()),
    nickname: v.optional(v.string()), // Custom nickname in this conversation
    isMuted: v.optional(v.boolean()),
    isBlocked: v.optional(v.boolean()),
  })
    .index("by_conversation", ["conversationId"])
    .index("by_user", ["userId"])
    .index("by_conversation_user", ["conversationId", "userId"]),

  // Messages with Mxit-style features
  messages: defineTable({
    conversationId: v.id("conversations"),
    senderId: v.id("users"),
    content: v.string(),
    type: v.union(
      v.literal("text"), 
      v.literal("image"), 
      v.literal("file"), 
      v.literal("voice"), 
      v.literal("system"),
      v.literal("game"), // Mxit games
      v.literal("mood_update"),
      v.literal("sticker")
    ),
    timestamp: v.float64(),
    editedAt: v.optional(v.float64()),
    replyToId: v.optional(v.id("messages")),
    // File/media fields
    fileUrl: v.optional(v.string()),
    fileName: v.optional(v.string()),
    fileSize: v.optional(v.number()),
    // Game fields
    gameType: v.optional(v.string()),
    gameData: v.optional(v.string()), // JSON string
    // Reactions (Mxit-style)
    reactions: v.optional(v.array(v.object({
      emoji: v.string(),
      userId: v.id("users"),
      timestamp: v.number(),
    }))),
    isDeleted: v.optional(v.boolean()),
  })
    .index("by_conversation", ["conversationId"])
    .index("by_sender", ["senderId"])
    .index("by_timestamp", ["timestamp"])
    .index("by_conversation_timestamp", ["conversationId", "timestamp"]),

  // Friends/Contacts with Mxit-style features
  friendships: defineTable({
    userId: v.id("users"),
    friendId: v.id("users"),
    status: v.union(v.literal("pending"), v.literal("accepted"), v.literal("blocked")),
    createdAt: v.number(),
    acceptedAt: v.optional(v.number()),
    // Mxit-style friendship features
    nickname: v.optional(v.string()), // Custom nickname for friend
    category: v.optional(v.string()), // "school", "work", "braai buddies", etc.
    isFavorite: v.optional(v.boolean()),
    sharedMemories: v.optional(v.number()), // Count of shared experiences
  })
    .index("by_user", ["userId"])
    .index("by_friend", ["friendId"])
    .index("by_user_friend", ["userId", "friendId"])
    .index("by_status", ["status"]),

  // Mxit-style games and activities
  games: defineTable({
    name: v.string(),
    description: v.string(),
    type: v.union(v.literal("trivia"), v.literal("word"), v.literal("puzzle"), v.literal("social")),
    isActive: v.boolean(),
    createdAt: v.number(),
    playCount: v.number(),
    // SA-themed games
    theme: v.optional(v.string()), // "rugby", "braai", "sa-history", etc.
    difficulty: v.optional(v.string()),
    maxPlayers: v.optional(v.number()),
  })
    .index("by_type", ["type"])
    .index("by_theme", ["theme"]),

  // Game sessions
  gameSessions: defineTable({
    gameId: v.id("games"),
    conversationId: v.id("conversations"),
    createdBy: v.id("users"),
    players: v.array(v.id("users")),
    status: v.union(v.literal("waiting"), v.literal("active"), v.literal("completed")),
    gameData: v.string(), // JSON string with game state
    startedAt: v.number(),
    completedAt: v.optional(v.number()),
    winner: v.optional(v.id("users")),
    scores: v.optional(v.array(v.object({
      userId: v.id("users"),
      score: v.number(),
    }))),
  })
    .index("by_game", ["gameId"])
    .index("by_conversation", ["conversationId"])
    .index("by_status", ["status"]),

  // File storage metadata
  files: defineTable({
    uploadedBy: v.id("users"),
    fileName: v.string(),
    fileType: v.string(),
    fileSize: v.number(),
    storageId: v.string(), // Convex file storage ID
    uploadedAt: v.number(),
    isPublic: v.boolean(),
    conversationId: v.optional(v.id("conversations")),
    messageId: v.optional(v.id("messages")),
  })
    .index("by_uploader", ["uploadedBy"])
    .index("by_conversation", ["conversationId"]),

  // Notifications
  notifications: defineTable({
    userId: v.id("users"),
    type: v.union(
      v.literal("message"), 
      v.literal("friend_request"), 
      v.literal("game_invite"),
      v.literal("room_invite"),
      v.literal("mood_update")
    ),
    title: v.string(),
    content: v.string(),
    isRead: v.boolean(),
    createdAt: v.number(),
    relatedId: v.optional(v.string()), // ID of related entity
    actionUrl: v.optional(v.string()),
  })
    .index("by_user", ["userId"])
    .index("by_user_read", ["userId", "isRead"]),
});
