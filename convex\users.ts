import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Create or update user from Clerk
export const createUser = mutation({
  args: {
    clerkId: v.string(),
    email: v.string(),
    username: v.string(),
    nickname: v.optional(v.string()),
    avatar: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
      .first();

    if (existingUser) {
      // Update existing user
      await ctx.db.patch(existingUser._id, {
        email: args.email,
        username: args.username,
        nickname: args.nickname,
        avatar: args.avatar,
        lastSeen: Date.now(),
      });
      return existingUser._id;
    }

    // Create new user with SA-themed defaults
    const userId = await ctx.db.insert("users", {
      clerkId: args.clerkId,
      email: args.email,
      username: args.username,
      nickname: args.nickname || args.username,
      avatar: args.avatar,
      mood: "Just joined XitChats! 🇿🇦",
      status: "online",
      joinedAt: Date.now(),
      lastSeen: Date.now(),
      isVerified: false,
      aboutMe: "New to XitChats, ready to vibe!",
      favoriteEmoji: "😊",
      braaiScore: 0,
    });

    return userId;
  },
});

// Get current user
export const getCurrentUser = query({
  args: { clerkId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
      .first();
  },
});

// Update user profile
export const updateProfile = mutation({
  args: {
    userId: v.id("users"),
    nickname: v.optional(v.string()),
    mood: v.optional(v.string()),
    status: v.optional(v.union(v.literal("online"), v.literal("away"), v.literal("busy"), v.literal("offline"))),
    location: v.optional(v.string()),
    language: v.optional(v.string()),
    aboutMe: v.optional(v.string()),
    favoriteEmoji: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { userId, ...updates } = args;
    
    await ctx.db.patch(userId, {
      ...updates,
      lastSeen: Date.now(),
    });

    // If mood changed, create a system message in active conversations
    if (updates.mood) {
      const userConversations = await ctx.db
        .query("conversationMembers")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .collect();

      const user = await ctx.db.get(userId);
      if (user) {
        for (const membership of userConversations) {
          await ctx.db.insert("messages", {
            conversationId: membership.conversationId,
            senderId: userId,
            content: `${user.nickname || user.username} changed their mood to: ${updates.mood}`,
            type: "mood_update",
            timestamp: Date.now(),
          });
        }
      }
    }
  },
});

// Update user status
export const updateStatus = mutation({
  args: {
    userId: v.id("users"),
    status: v.union(v.literal("online"), v.literal("away"), v.literal("busy"), v.literal("offline")),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      status: args.status,
      lastSeen: Date.now(),
    });
  },
});

// Search users (for adding friends)
export const searchUsers = query({
  args: { 
    query: v.string(),
    currentUserId: v.id("users"),
  },
  handler: async (ctx, args) => {
    if (args.query.length < 2) return [];

    const users = await ctx.db.query("users").collect();
    
    return users
      .filter(user => 
        user._id !== args.currentUserId &&
        (user.username.toLowerCase().includes(args.query.toLowerCase()) ||
         user.nickname?.toLowerCase().includes(args.query.toLowerCase()) ||
         user.email.toLowerCase().includes(args.query.toLowerCase()))
      )
      .slice(0, 10)
      .map(user => ({
        _id: user._id,
        username: user.username,
        nickname: user.nickname,
        avatar: user.avatar,
        mood: user.mood,
        status: user.status,
        location: user.location,
      }));
  },
});

// Get user's friends
export const getFriends = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const friendships = await ctx.db
      .query("friendships")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("status"), "accepted"))
      .collect();

    const friends = await Promise.all(
      friendships.map(async (friendship) => {
        const friend = await ctx.db.get(friendship.friendId);
        if (!friend) return null;

        // Get last message between users
        const conversations = await ctx.db
          .query("conversationMembers")
          .withIndex("by_user", (q) => q.eq("userId", args.userId))
          .collect();

        let lastMessage = null;
        let unreadCount = 0;

        for (const membership of conversations) {
          const conversation = await ctx.db.get(membership.conversationId);
          if (conversation?.type === "private") {
            const otherMember = await ctx.db
              .query("conversationMembers")
              .withIndex("by_conversation", (q) => q.eq("conversationId", membership.conversationId))
              .filter((q) => q.neq(q.field("userId"), args.userId))
              .first();

            if (otherMember?.userId === friendship.friendId) {
              const messages = await ctx.db
                .query("messages")
                .withIndex("by_conversation_timestamp", (q) => 
                  q.eq("conversationId", membership.conversationId)
                )
                .order("desc")
                .take(1);

              if (messages.length > 0) {
                lastMessage = messages[0];
              }

              // Count unread messages
              const unreadMessages = await ctx.db
                .query("messages")
                .withIndex("by_conversation_timestamp", (q) => 
                  q.eq("conversationId", membership.conversationId)
                )
                .filter((q) => 
                  q.and(
                    q.gt(q.field("timestamp"), membership.lastReadAt || 0),
                    q.neq(q.field("senderId"), args.userId)
                  )
                )
                .collect();

              unreadCount = unreadMessages.length;
              break;
            }
          }
        }

        return {
          id: friend._id,
          name: friend.username,
          nickname: friendship.nickname || friend.nickname,
          avatar: friend.avatar,
          status: friend.status,
          mood: friend.mood,
          lastMessage: lastMessage?.content,
          lastSeen: friend.lastSeen,
          unreadCount,
          isFavorite: friendship.isFavorite,
          category: friendship.category,
        };
      })
    );

    return friends.filter(Boolean);
  },
});

// Get online friends count
export const getOnlineFriendsCount = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const friendships = await ctx.db
      .query("friendships")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("status"), "accepted"))
      .collect();

    let onlineCount = 0;
    for (const friendship of friendships) {
      const friend = await ctx.db.get(friendship.friendId);
      if (friend?.status === "online") {
        onlineCount++;
      }
    }

    return onlineCount;
  },
});
