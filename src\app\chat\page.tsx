"use client"

import { useUser } from "@clerk/nextjs"
import { useQuery, useMutation } from "convex/react"
import { api } from "../../../convex/_generated/api"
import { ChatInterface } from "@/components/mxit/ChatInterface"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

export default function ChatPage() {
  const { user, isLoaded } = useUser()
  const router = useRouter()
  const [isMobile, setIsMobile] = useState(false)
  
  const createUser = useMutation(api.users.createUser)
  const currentUser = useQuery(
    api.users.getCurrentUser,
    user ? { clerkId: user.id } : "skip"
  )

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    if (!isLoaded) return
    
    if (!user) {
      router.push("/sign-in")
      return
    }

    // Create user in Convex if they don't exist
    if (user && currentUser === null) {
      createUser({
        clerkId: user.id,
        email: user.emailAddresses[0]?.emailAddress || "",
        username: user.username || user.firstName || "user",
        nickname: user.firstName || "New User",
        avatar: user.imageUrl,
      })
    }
  }, [user, currentUser, isLoaded, createUser, router])

  if (!isLoaded || !user) {
    return (
      <div className="h-screen flex items-center justify-center bg-mxit-blue-500">
        <div className="text-center text-white">
          <div className="w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold mb-2">XitChats</h2>
          <p>Loading your chats... 🇿🇦</p>
        </div>
      </div>
    )
  }

  if (!currentUser) {
    return (
      <div className="h-screen flex items-center justify-center bg-mxit-blue-500">
        <div className="text-center text-white">
          <div className="w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold mb-2">XitChats</h2>
          <p>Setting up your profile... 🇿🇦</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen overflow-hidden">
      <ChatInterface 
        currentUserId={currentUser._id} 
        isMobile={isMobile}
      />
    </div>
  )
}
