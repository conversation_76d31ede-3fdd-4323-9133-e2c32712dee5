import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ClerkProvider } from '@clerk/nextjs';
import { ConvexClientProvider } from '@/components/providers/ConvexClientProvider';
import { Toaster } from '@/components/ui/toaster';

// Initialize Inter font with modern font-display and optimization
const inter = Inter({ 
  subsets: ["latin"],
  display: 'swap',
  variable: '--font-inter',
  preload: true,
  fallback: ['system-ui', 'sans-serif'],
});

export const metadata: Metadata = {
  title: {
    default: "XitChats - Feel the Mxit Vibe Again! 🇿🇦",
    template: "%s | XitChats",
  },
  description: "The nostalgic chat app that brings back the golden days of Mxit for South Africans. Chat, connect, and relive the memories!",
  keywords: ["Mxit", "South Africa", "chat", "messaging", "nostalgia", "SA", "braai", "friends", "social"],
  authors: [{ 
    name: "XitChats Team",
    url: "https://xitchats.app" 
  }],
  creator: "XitChats Team",
  publisher: "XitChats",
  openGraph: {
    title: "XitChats - Feel the Mxit Vibe Again!",
    description: "The nostalgic chat app for South Africans",
    type: "website",
    locale: "en_ZA",
    url: "https://xitchats.app",
    siteName: "XitChats",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "XitChats - Feel the Mxit Vibe Again!",
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'XitChats - Feel the Mxit Vibe Again!',
    description: 'The nostalgic chat app for South Africans',
    creator: '@xitchats',
    images: ['/twitter-image.jpg'],
  },
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
  },
  manifest: '/site.webmanifest',
  metadataBase: new URL('https://xitchats.app'),
  alternates: {
    canonical: '/',
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0a0a0a' },
  ],
  colorScheme: 'light dark',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider
      appearance={{
        variables: {
          colorPrimary: 'hsl(210, 100%, 40%)',
          colorBackground: 'hsl(0, 0%, 100%)',
          colorInputBackground: 'hsl(0, 0%, 98%)',
          colorInputText: 'hsl(0, 0%, 9%)',
          colorText: 'hsl(0, 0%, 9%)',
          colorTextSecondary: 'hsl(0, 0%, 45%)',
          colorSuccess: 'hsl(142, 71%, 45%)',
          colorDanger: 'hsl(0, 84%, 60%)',
          colorWarning: 'hsl(38, 92%, 50%)',
          borderRadius: '0.75rem',
          fontFamily: 'var(--font-inter), system-ui, sans-serif',
          spacingUnit: '0.25rem',
          fontSize: '1rem',
          fontWeight: {
            normal: '400',
            medium: '500',
            semibold: '600',
            bold: '700',
          },
        },
        elements: {
          // Buttons
          formButtonPrimary: {
            backgroundColor: 'hsl(210, 100%, 40%)',
            borderRadius: '0.5rem',
            fontWeight: '500',
            padding: '0.625rem 1.25rem',
            transition: 'all 0.2s ease',
            '&:hover, &:focus, &:active': {
              backgroundColor: 'hsl(210, 100%, 35%)',
              transform: 'translateY(-1px)',
            },
            '&:focus': {
              boxShadow: '0 0 0 3px hsla(210, 100%, 40%, 0.2)',
            },
            '&:active': {
              transform: 'translateY(0)',
            },
          },
          // Cards
          card: {
            boxShadow: '0 8px 32px hsla(210, 100%, 40%, 0.1)',
            border: '1px solid hsla(0, 0%, 0%, 0.08)',
            borderRadius: '1rem',
            overflow: 'hidden',
            backgroundColor: 'hsl(0, 0%, 100%)',
          },
          // Headers
          headerTitle: {
            color: 'hsl(210, 100%, 40%)',
            fontWeight: '700',
            fontSize: '1.5rem',
            lineHeight: '2rem',
          },
          headerSubtitle: {
            color: 'hsl(0, 0%, 45%)',
            fontSize: '1rem',
            lineHeight: '1.5rem',
          },
          // Social Buttons
          socialButtonsBlockButton: {
            border: '1px solid hsla(210, 100%, 40%, 0.2)',
            borderRadius: '0.5rem',
            transition: 'all 0.2s ease',
            '&:hover': {
              backgroundColor: 'hsla(210, 100%, 40%, 0.05)',
              borderColor: 'hsla(210, 100%, 40%, 0.3)',
            },
          },
          // Form Inputs
          formFieldInput: {
            border: '1px solid hsla(0, 0%, 0%, 0.1)',
            borderRadius: '0.5rem',
            padding: '0.75rem 1rem',
            fontSize: '1rem',
            transition: 'all 0.2s ease',
            '&:focus': {
              borderColor: 'hsl(210, 100%, 40%)',
              boxShadow: '0 0 0 3px hsla(210, 100%, 40%, 0.15)',
              outline: 'none',
            },
            '&::placeholder': {
              color: 'hsl(0, 0%, 65%)',
            },
          },
          // Links in footer
          footerActionLink: {
            color: 'hsl(210, 100%, 40%)',
            transition: 'all 0.2s ease',
            '&:hover': {
              color: 'hsl(210, 100%, 35%)',
              textDecoration: 'underline',
            },
          },
          // Active states for navigation
          activeLink: {
            color: 'hsl(210, 100%, 40%)',
            fontWeight: '600',
          },
          // Loading states
          spinner: {
            primary: 'hsl(210, 100%, 40%)',
            secondary: 'hsla(210, 100%, 40%, 0.1)',
          },
        },
        layout: {
          logoImageUrl: '/logo.svg',
          logoPlacement: 'inside',
          socialButtonsPlacement: 'bottom',
          socialButtonsVariant: 'iconButton',
        },
      }}
    >
      <html 
        lang="en-ZA" 
        className={`${inter.variable} scroll-smooth`} 
        suppressHydrationWarning
        style={{
          '--font-inter': inter.style.fontFamily,
          '--color-primary': 'hsl(210, 100%, 40%)',
          '--color-background': 'hsl(0, 0%, 100%)',
          '--color-foreground': 'hsl(0, 0%, 9%)',
        } as React.CSSProperties}
      >
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, viewport-fit=cover" />
          <meta name="theme-color" content="#0066cc" />
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
          <link rel="preload" as="style" href={inter.style.fontFamily} />
        </head>
        <body className="min-h-screen bg-background font-sans antialiased text-foreground">
          <ConvexClientProvider>
            <div className="relative flex min-h-screen flex-col">
              {/* <MainNav /> */}
              <main className="flex-1">
                <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                  {children}
                </div>
              </main>
              <Toaster />
            </div>
          </ConvexClientProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
