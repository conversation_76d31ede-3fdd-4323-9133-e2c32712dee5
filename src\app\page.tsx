"use client"

import { useUser, RedirectToSignIn } from "@clerk/nextjs"
import { useEffect, useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import { ChatInterface } from "@/components/mxit/ChatInterface"
// import { MobileApp } from "@/components/mobile/MobileApp"
import { StyleTest } from "@/components/test/StyleTest"
import { Button } from "@/components/ui/button"
import { Loader2, Smartphone, Monitor } from "lucide-react"


export default function Home() {
  const { isSignedIn, isLoaded, user } = useUser()
  const [isMobile, setIsMobile] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    const checkMobile = () => {
      const mobileCheck = window.innerWidth < 768
      setIsMobile(mobileCheck)
      
      // Show a toast when switching between mobile and desktop views
      if (mobileCheck !== isMobile) {
        toast({
          title: mobileCheck ? "Mobile View" : "Desktop View",
          description: mobileCheck 
            ? "You're viewing the mobile-optimized interface"
            : "You're viewing the desktop interface",
        })
      }
      
      setIsLoading(false)
    }

    // Initial check
    checkMobile()

    // Add resize listener
    window.addEventListener('resize', checkMobile)

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkMobile)
    }
  }, [isMobile, toast])

  // Show loading state
  if (!isLoaded || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-mxit-blue-500 to-mxit-blue-700">
        <div className="text-center p-8 bg-white/90 dark:bg-gray-900/90 rounded-2xl shadow-xl max-w-md w-full mx-4">
          <div className="flex justify-center mb-6">
            <div className="w-20 h-20 rounded-full bg-mxit-blue-100 dark:bg-mxit-blue-900 flex items-center justify-center">
              <Loader2 className="h-10 w-10 text-mxit-blue-500 animate-spin" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">XitChats</h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">Loading the vibe... 🇿🇦</p>
          <div className="flex justify-center space-x-4">
            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
              <Smartphone className="h-4 w-4 mr-1" />
              <span>Mobile</span>
            </div>
            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
              <Monitor className="h-4 w-4 mr-1" />
              <span>Desktop</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Redirect to sign in if not signed in
  if (!isSignedIn) {
    return <RedirectToSignIn />
  }

  // Show mobile app for mobile devices, desktop app for larger screens
  // if (isMobile) {
  //   return <MobileApp currentUserId={user?.id || "current-user"} />
  // }

  // Desktop view
  return (
    <ChatInterface
      currentUserId={user?.id || "current-user"}
      isMobile={false}
    />
  )
}
