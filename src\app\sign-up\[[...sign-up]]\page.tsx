import { SignUp } from "@clerk/nextjs"

export default function SignUpPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-mxit-blue-500 to-mxit-blue-700 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">XitChats</h1>
          <p className="text-mxit-blue-100">Join the Mzansi vibe! 🇿🇦</p>
        </div>

        <div className="bg-white rounded-2xl shadow-2xl p-8">
          <SignUp
            appearance={{
              elements: {
                formButtonPrimary: "bg-mxit-blue-500 hover:bg-mxit-blue-600 text-white",
                card: "shadow-none",
                headerTitle: "text-mxit-blue-600",
                headerSubtitle: "text-gray-600",
                socialButtonsBlockButton: "border-mxit-blue-200 hover:bg-mxit-blue-50",
                formFieldInput: "border-gray-300 focus:border-mxit-blue-500 focus:ring-mxit-blue-200",
                footerActionLink: "text-mxit-blue-600 hover:text-mxit-blue-700"
              }
            }}
            forceRedirectUrl="/onboarding"
          />
        </div>

        <div className="text-center mt-6 text-mxit-blue-100 text-sm">
          <p>"Howzit! Ready to join the lekker chat community? Sharp!" - Welcome to XitChats</p>
        </div>
      </div>
    </div>
  )
}
