"use client"

import { SignIn } from "@clerk/nextjs"
import { Heart, MessageCircle, Users, Zap } from "lucide-react"

export function MxitSignIn() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-mxit-blue-500 via-mxit-blue-600 to-mxit-blue-700 flex items-center justify-center p-4">
      <div className="max-w-6xl w-full grid lg:grid-cols-2 gap-8 items-center">
        {/* Left side - Branding */}
        <div className="text-white space-y-8 text-center lg:text-left">
          <div className="space-y-4">
            <h1 className="text-5xl lg:text-6xl font-bold">
              XitChats
            </h1>
            <p className="text-xl lg:text-2xl text-mxit-blue-100">
              Feel the Mxit vibe again! 🇿🇦
            </p>
            <p className="text-lg text-mxit-blue-200 max-w-lg">
              Welcome back to the golden days of South African chat culture. 
              Connect with friends, join rooms, and relive those lekker memories!
            </p>
          </div>

          {/* Features */}
          <div className="grid grid-cols-2 gap-6 max-w-lg mx-auto lg:mx-0">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-mxit-orange-500 rounded-full flex items-center justify-center">
                <MessageCircle className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold">Chat</h3>
                <p className="text-sm text-mxit-blue-200">Real-time messaging</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-mxit-orange-500 rounded-full flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold">Rooms</h3>
                <p className="text-sm text-mxit-blue-200">Join public chats</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-mxit-orange-500 rounded-full flex items-center justify-center">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold">Games</h3>
                <p className="text-sm text-mxit-blue-200">Play together</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-mxit-orange-500 rounded-full flex items-center justify-center">
                <Heart className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold">Nostalgia</h3>
                <p className="text-sm text-mxit-blue-200">Pure SA vibes</p>
              </div>
            </div>
          </div>

          {/* SA Pride */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 max-w-lg mx-auto lg:mx-0">
            <h3 className="text-xl font-semibold mb-3">Made for Mzansi 🇿🇦</h3>
            <p className="text-mxit-blue-100">
              "Eish, this takes me back to the good old days! Sharp sharp, let's chat!" 
              - Every South African who remembers Mxit
            </p>
          </div>

          {/* Fun stats */}
          <div className="flex justify-center lg:justify-start gap-8 text-center">
            <div>
              <div className="text-2xl font-bold">2005</div>
              <div className="text-sm text-mxit-blue-200">Mxit launched</div>
            </div>
            <div>
              <div className="text-2xl font-bold">50M+</div>
              <div className="text-sm text-mxit-blue-200">Users at peak</div>
            </div>
            <div>
              <div className="text-2xl font-bold">∞</div>
              <div className="text-sm text-mxit-blue-200">Memories made</div>
            </div>
          </div>
        </div>

        {/* Right side - Sign In */}
        <div className="flex justify-center">
          <div className="bg-white rounded-3xl shadow-2xl p-8 w-full max-w-md">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Welcome Back!
              </h2>
              <p className="text-gray-600">
                Sign in to start chatting with your friends
              </p>
            </div>

            <SignIn 
              appearance={{
                elements: {
                  rootBox: "w-full",
                  card: "shadow-none border-none bg-transparent",
                  headerTitle: "hidden",
                  headerSubtitle: "hidden",
                  socialButtonsBlockButton: "w-full justify-center",
                  formButtonPrimary: "w-full bg-mxit-blue-500 hover:bg-mxit-blue-600",
                  footerActionLink: "text-mxit-blue-500 hover:text-mxit-blue-600",
                }
              }}
              redirectUrl="/chat"
            />

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500">
                New to XitChats?{" "}
                <a href="/sign-up" className="text-mxit-blue-500 hover:text-mxit-blue-600 font-medium">
                  Create an account
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-mxit-orange-400/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-mxit-orange-400/20 rounded-full blur-3xl"></div>
      </div>
    </div>
  )
}
