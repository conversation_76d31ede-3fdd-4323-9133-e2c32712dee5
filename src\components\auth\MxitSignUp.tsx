"use client"

import { SignUp } from "@clerk/nextjs"
import { <PERSON><PERSON><PERSON>, <PERSON>, MessageCircle, Heart } from "lucide-react"

export function MxitSignUp() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-mxit-orange-500 via-mxit-orange-600 to-mxit-blue-600 flex items-center justify-center p-4">
      <div className="max-w-6xl w-full grid lg:grid-cols-2 gap-8 items-center">
        {/* Left side - Sign Up */}
        <div className="flex justify-center order-2 lg:order-1">
          <div className="bg-white rounded-3xl shadow-2xl p-8 w-full max-w-md">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-mxit-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Join <PERSON>!
              </h2>
              <p className="text-gray-600">
                Create your account and start the nostalgia trip
              </p>
            </div>

            <SignUp 
              appearance={{
                elements: {
                  rootBox: "w-full",
                  card: "shadow-none border-none bg-transparent",
                  headerTitle: "hidden",
                  headerSubtitle: "hidden",
                  socialButtonsBlockButton: "w-full justify-center",
                  formButtonPrimary: "w-full bg-mxit-blue-500 hover:bg-mxit-blue-600",
                  footerActionLink: "text-mxit-blue-500 hover:text-mxit-blue-600",
                }
              }}
              redirectUrl="/onboarding"
            />

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500">
                Already have an account?{" "}
                <a href="/sign-in" className="text-mxit-blue-500 hover:text-mxit-blue-600 font-medium">
                  Sign in
                </a>
              </p>
            </div>
          </div>
        </div>

        {/* Right side - Branding */}
        <div className="text-white space-y-8 text-center lg:text-left order-1 lg:order-2">
          <div className="space-y-4">
            <h1 className="text-5xl lg:text-6xl font-bold">
              Ready to vibe?
            </h1>
            <p className="text-xl lg:text-2xl text-white/90">
              Join thousands of South Africans reliving the Mxit magic! ✨
            </p>
          </div>

          {/* What you'll get */}
          <div className="space-y-6">
            <h3 className="text-2xl font-semibold">What you'll get:</h3>
            
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <MessageCircle className="w-5 h-5" />
                </div>
                <div>
                  <h4 className="font-semibold">Instant Messaging</h4>
                  <p className="text-white/80 text-sm">Chat with friends in real-time, just like the old days</p>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <Users className="w-5 h-5" />
                </div>
                <div>
                  <h4 className="font-semibold">Chat Rooms</h4>
                  <p className="text-white/80 text-sm">Join public rooms and meet new people</p>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <Heart className="w-5 h-5" />
                </div>
                <div>
                  <h4 className="font-semibold">Mood Messages</h4>
                  <p className="text-white/80 text-sm">Express yourself with custom mood updates</p>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <Sparkles className="w-5 h-5" />
                </div>
                <div>
                  <h4 className="font-semibold">Games & Fun</h4>
                  <p className="text-white/80 text-sm">Play games and share memories with friends</p>
                </div>
              </div>
            </div>
          </div>

          {/* Testimonial */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-mxit-blue-400 rounded-full flex items-center justify-center text-white font-bold">
                T
              </div>
              <div>
                <div className="font-semibold">Thabo from Joburg</div>
                <div className="text-sm text-white/70">Early XitChats user</div>
              </div>
            </div>
            <p className="text-white/90 italic">
              "Eish bru, this app brought back so many memories! It's like Mxit never left. 
              The vibes are exactly the same - lekker!"
            </p>
          </div>

          {/* SA elements */}
          <div className="flex justify-center lg:justify-start gap-4 text-4xl">
            <span>🇿🇦</span>
            <span>🍖</span>
            <span>🏉</span>
            <span>☀️</span>
            <span>💎</span>
          </div>
        </div>
      </div>

      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-mxit-blue-400/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -right-40 w-80 h-80 bg-mxit-blue-400/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
      </div>
    </div>
  )
}
