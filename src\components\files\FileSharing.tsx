"use client"

import { useState } from "react"
import { Upload, File, Image, Music, Video, Download, X, FileText, Archive } from "lucide-react"

interface FileItem {
  id: string
  name: string
  size: number
  type: string
  uploadedAt: Date
  uploadedBy: string
  url?: string
}

interface FileSharingProps {
  onClose: () => void
  onFileUpload: (file: File) => void
  onFileDownload: (fileId: string) => void
}

export function FileSharing({ onClose, onFileUpload, onFileDownload }: FileSharingProps) {
  const [dragActive, setDragActive] = useState(false)
  const [selectedTab, setSelectedTab] = useState<'upload' | 'recent'>('upload')

  // Mock recent files - in real app this would come from Convex
  const recentFiles: FileItem[] = [
    {
      id: '1',
      name: 'braai_pics_2024.zip',
      size: 15728640, // 15MB
      type: 'application/zip',
      uploadedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      uploadedBy: 'T-Money'
    },
    {
      id: '2',
      name: 'springboks_highlights.mp4',
      size: 52428800, // 50MB
      type: 'video/mp4',
      uploadedAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
      uploadedBy: 'Noms'
    },
    {
      id: '3',
      name: 'amapiano_mix.mp3',
      size: 8388608, // 8MB
      type: 'audio/mp3',
      uploadedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      uploadedBy: 'Sipho'
    },
    {
      id: '4',
      name: 'recipe_potjiekos.pdf',
      size: 1048576, // 1MB
      type: 'application/pdf',
      uploadedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      uploadedBy: 'Tannie Marie'
    }
  ]

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      onFileUpload(e.dataTransfer.files[0])
    }
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      onFileUpload(e.target.files[0])
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <Image className="w-5 h-5" />
    if (type.startsWith('video/')) return <Video className="w-5 h-5" />
    if (type.startsWith('audio/')) return <Music className="w-5 h-5" />
    if (type === 'application/pdf') return <FileText className="w-5 h-5" />
    if (type.includes('zip') || type.includes('rar')) return <Archive className="w-5 h-5" />
    return <File className="w-5 h-5" />
  }

  const getTimeAgo = (date: Date): string => {
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    return date.toLocaleDateString()
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="mxit-gradient p-6 text-white">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">File Sharing</h2>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          <p className="text-mxit-blue-100 mt-2">Share files with your friends - just like the old Mxit days! 📁</p>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setSelectedTab('upload')}
            className={`flex-1 py-3 px-4 text-sm font-medium transition-colors ${
              selectedTab === 'upload'
                ? 'text-mxit-blue-600 border-b-2 border-mxit-blue-600 bg-mxit-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Upload className="w-4 h-4 inline mr-2" />
            Upload Files
          </button>
          <button
            onClick={() => setSelectedTab('recent')}
            className={`flex-1 py-3 px-4 text-sm font-medium transition-colors ${
              selectedTab === 'recent'
                ? 'text-mxit-blue-600 border-b-2 border-mxit-blue-600 bg-mxit-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <File className="w-4 h-4 inline mr-2" />
            Recent Files
          </button>
        </div>

        {/* Content */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {selectedTab === 'upload' ? (
            <div className="space-y-6">
              {/* Upload Area */}
              <div
                className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors ${
                  dragActive
                    ? 'border-mxit-blue-500 bg-mxit-blue-50'
                    : 'border-gray-300 hover:border-mxit-blue-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Drop files here or click to browse
                </h3>
                <p className="text-gray-500 mb-4">
                  Share photos, videos, music, documents and more!
                </p>
                <input
                  type="file"
                  onChange={handleFileInput}
                  className="hidden"
                  id="file-upload"
                  multiple
                />
                <label
                  htmlFor="file-upload"
                  className="inline-flex items-center px-4 py-2 bg-mxit-blue-600 text-white rounded-lg hover:bg-mxit-blue-700 cursor-pointer transition-colors"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Choose Files
                </label>
              </div>

              {/* File Types Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Supported file types:</h4>
                <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                  <div>📸 Images (JPG, PNG, GIF)</div>
                  <div>🎵 Audio (MP3, WAV, M4A)</div>
                  <div>🎬 Videos (MP4, AVI, MOV)</div>
                  <div>📄 Documents (PDF, DOC, TXT)</div>
                  <div>📦 Archives (ZIP, RAR)</div>
                  <div>💾 Max size: 100MB</div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {recentFiles.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-mxit-blue-600">
                      {getFileIcon(file.type)}
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{file.name}</h4>
                      <p className="text-sm text-gray-500">
                        {formatFileSize(file.size)} • by {file.uploadedBy} • {getTimeAgo(file.uploadedAt)}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => onFileDownload(file.id)}
                    className="p-2 text-mxit-blue-600 hover:bg-mxit-blue-100 rounded-lg transition-colors"
                  >
                    <Download className="w-5 h-5" />
                  </button>
                </div>
              ))}
              
              {recentFiles.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <File className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No files shared yet</p>
                  <p className="text-sm">Upload some files to get started!</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
