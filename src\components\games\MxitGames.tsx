"use client"

import { useState } from "react"
import { Gamepad2, Trophy, Users, Clock, Star, Zap } from "lucide-react"
import { cn } from "@/lib/utils"

interface Game {
  id: string
  name: string
  description: string
  type: "trivia" | "word" | "puzzle" | "social"
  theme: string
  difficulty: "Easy" | "Medium" | "Hard"
  maxPlayers: number
  playCount: number
  icon: string
}

const SA_GAMES: Game[] = [
  {
    id: "sa-trivia",
    name: "<PERSON><PERSON><PERSON>",
    description: "Test your knowledge of South African history, culture, and current events!",
    type: "trivia",
    theme: "sa-culture",
    difficulty: "Medium",
    maxPlayers: 8,
    playCount: 1247,
    icon: "🇿🇦"
  },
  {
    id: "rugby-quiz",
    name: "Springbok Challenge",
    description: "How well do you know the Boks? Rugby trivia for true fans!",
    type: "trivia", 
    theme: "rugby",
    difficulty: "Hard",
    maxPlayers: 6,
    playCount: 892,
    icon: "🏉"
  },
  {
    id: "braai-master",
    name: "<PERSON><PERSON><PERSON> Master",
    description: "Plan the perfect braai! Match ingredients and cooking times.",
    type: "puzzle",
    theme: "braai",
    difficulty: "Easy",
    maxPlayers: 4,
    playCount: 2156,
    icon: "🍖"
  },
  {
    id: "word-chain",
    name: "Afrikaans Word Chain",
    description: "Build word chains in Afrikaans. Lekker brain exercise!",
    type: "word",
    theme: "language",
    difficulty: "Medium",
    maxPlayers: 10,
    playCount: 567,
    icon: "📝"
  },
  {
    id: "guess-location",
    name: "Guess the SA Location",
    description: "Can you identify these iconic South African places?",
    type: "trivia",
    theme: "geography",
    difficulty: "Medium",
    maxPlayers: 8,
    playCount: 1034,
    icon: "🏔️"
  },
  {
    id: "emoji-story",
    name: "Emoji Story Builder",
    description: "Create stories using only emojis. Others guess the story!",
    type: "social",
    theme: "creative",
    difficulty: "Easy",
    maxPlayers: 12,
    playCount: 3421,
    icon: "😊"
  }
]

interface MxitGamesProps {
  onStartGame: (gameId: string) => void
  onClose: () => void
}

export function MxitGames({ onStartGame, onClose }: MxitGamesProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [selectedGame, setSelectedGame] = useState<Game | null>(null)

  const categories = [
    { id: "all", name: "All Games", icon: "🎮" },
    { id: "trivia", name: "Trivia", icon: "🧠" },
    { id: "word", name: "Word Games", icon: "📝" },
    { id: "puzzle", name: "Puzzles", icon: "🧩" },
    { id: "social", name: "Social", icon: "👥" },
  ]

  const filteredGames = selectedCategory === "all" 
    ? SA_GAMES 
    : SA_GAMES.filter(game => game.type === selectedCategory)

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Easy": return "text-green-600 bg-green-100"
      case "Medium": return "text-yellow-600 bg-yellow-100"
      case "Hard": return "text-red-600 bg-red-100"
      default: return "text-gray-600 bg-gray-100"
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="mxit-gradient p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Gamepad2 className="w-8 h-8" />
              <div>
                <h2 className="text-2xl font-bold">Mxit Games</h2>
                <p className="text-mxit-blue-100">Play together, have fun! 🎮</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center transition-colors"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="flex h-[600px]">
          {/* Sidebar */}
          <div className="w-64 border-r border-gray-200 p-4">
            <h3 className="font-semibold text-gray-900 mb-4">Categories</h3>
            <div className="space-y-2">
              {categories.map(category => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors",
                    selectedCategory === category.id
                      ? "bg-mxit-blue-100 text-mxit-blue-700"
                      : "hover:bg-gray-100"
                  )}
                >
                  <span className="text-lg">{category.icon}</span>
                  <span className="font-medium">{category.name}</span>
                </button>
              ))}
            </div>

            {/* Stats */}
            <div className="mt-8 p-4 bg-mxit-orange-50 rounded-lg">
              <h4 className="font-semibold text-mxit-orange-800 mb-2">Your Stats</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Games Played:</span>
                  <span className="font-semibold">23</span>
                </div>
                <div className="flex justify-between">
                  <span>Wins:</span>
                  <span className="font-semibold">15</span>
                </div>
                <div className="flex justify-between">
                  <span>Braai Score:</span>
                  <span className="font-semibold">🍖🍖🍖</span>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {!selectedGame ? (
              <>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold">
                    {categories.find(c => c.id === selectedCategory)?.name} 
                    <span className="text-gray-500 ml-2">({filteredGames.length})</span>
                  </h3>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Trophy className="w-4 h-4" />
                    <span>Play to earn Braai Points!</span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {filteredGames.map(game => (
                    <div
                      key={game.id}
                      onClick={() => setSelectedGame(game)}
                      className="border border-gray-200 rounded-xl p-4 hover:shadow-lg transition-all cursor-pointer hover:border-mxit-blue-300"
                    >
                      <div className="flex items-start gap-3">
                        <div className="text-3xl">{game.icon}</div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-semibold text-gray-900">{game.name}</h4>
                            <span className={cn(
                              "px-2 py-1 rounded-full text-xs font-medium",
                              getDifficultyColor(game.difficulty)
                            )}>
                              {game.difficulty}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-3">{game.description}</p>
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <div className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              <span>Up to {game.maxPlayers}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Star className="w-3 h-3" />
                              <span>{game.playCount} plays</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              /* Game Details */
              <div className="space-y-6">
                <button
                  onClick={() => setSelectedGame(null)}
                  className="text-mxit-blue-600 hover:text-mxit-blue-700 font-medium"
                >
                  ← Back to games
                </button>

                <div className="bg-gradient-to-r from-mxit-blue-50 to-mxit-orange-50 rounded-2xl p-6">
                  <div className="flex items-start gap-4">
                    <div className="text-6xl">{selectedGame.icon}</div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">{selectedGame.name}</h3>
                      <p className="text-gray-700 mb-4">{selectedGame.description}</p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-mxit-blue-600">{selectedGame.maxPlayers}</div>
                          <div className="text-sm text-gray-600">Max Players</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-mxit-orange-600">{selectedGame.difficulty}</div>
                          <div className="text-sm text-gray-600">Difficulty</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">{selectedGame.playCount}</div>
                          <div className="text-sm text-gray-600">Times Played</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">5-15</div>
                          <div className="text-sm text-gray-600">Minutes</div>
                        </div>
                      </div>

                      <div className="flex gap-3">
                        <button
                          onClick={() => onStartGame(selectedGame.id)}
                          className="flex items-center gap-2 px-6 py-3 bg-mxit-blue-500 text-white rounded-lg hover:bg-mxit-blue-600 transition-colors"
                        >
                          <Zap className="w-4 h-4" />
                          Start Game
                        </button>
                        <button className="flex items-center gap-2 px-6 py-3 border border-mxit-blue-500 text-mxit-blue-600 rounded-lg hover:bg-mxit-blue-50 transition-colors">
                          <Users className="w-4 h-4" />
                          Invite Friends
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Game Rules */}
                <div className="bg-white border border-gray-200 rounded-xl p-6">
                  <h4 className="font-semibold text-gray-900 mb-3">How to Play</h4>
                  <div className="space-y-2 text-sm text-gray-700">
                    {selectedGame.id === "sa-trivia" && (
                      <>
                        <p>• Answer questions about South African culture, history, and current events</p>
                        <p>• Each correct answer earns you points</p>
                        <p>• Fastest correct answers get bonus points</p>
                        <p>• Winner gets braai points and bragging rights!</p>
                      </>
                    )}
                    {selectedGame.id === "braai-master" && (
                      <>
                        <p>• Plan the perfect braai by matching ingredients</p>
                        <p>• Time your cooking to perfection</p>
                        <p>• Don't burn the boerewors!</p>
                        <p>• Most lekker braai wins!</p>
                      </>
                    )}
                    {selectedGame.id === "emoji-story" && (
                      <>
                        <p>• Create a story using only emojis</p>
                        <p>• Other players guess what your story means</p>
                        <p>• Points for creative stories and correct guesses</p>
                        <p>• Most entertaining storyteller wins!</p>
                      </>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
