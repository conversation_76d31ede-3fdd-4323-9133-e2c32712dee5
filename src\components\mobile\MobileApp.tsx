"use client"

import { useState, useEffect } from "react"
import { ChatInterface } from "@/components/mxit/ChatInterface"

export function MobileApp({ currentUserId }: { currentUserId: string }) {
  const [isMobile, setIsMobile] = useState(true)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return (
    <div className="h-screen w-full bg-gray-50 overflow-hidden">
      {/* Mobile Status Bar */}
      <div className="bg-black text-white text-xs px-4 py-1 flex justify-between items-center">
        <span>9:41</span>
        <span>XitChats</span>
        <div className="flex items-center gap-1">
          <span>📶</span>
          <span>📶</span>
          <span>🔋</span>
        </div>
      </div>
      
      {/* Main App */}
      <div className="h-[calc(100vh-24px)]">
        <ChatInterface 
          currentUserId={currentUserId} 
          isMobile={true}
        />
      </div>
    </div>
  )
}
