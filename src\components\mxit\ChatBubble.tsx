"use client"

import { cn, formatTime, convertEmoticons } from "@/lib/utils"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface ChatBubbleProps {
  message: string
  timestamp: Date
  isOwn: boolean
  senderName?: string
  senderAvatar?: string
  isSystem?: boolean
}

export function ChatBubble({ 
  message, 
  timestamp, 
  isOwn, 
  senderName, 
  senderAvatar,
  isSystem = false 
}: ChatBubbleProps) {
  const processedMessage = convertEmoticons(message)
  
  if (isSystem) {
    return (
      <div className="flex justify-center my-4">
        <div className="bg-gradient-to-r from-mxit-orange-100 to-mxit-orange-200 dark:from-mxit-orange-900 dark:to-mxit-orange-800 text-mxit-orange-800 dark:text-mxit-orange-200 px-4 py-2 rounded-full text-sm font-medium shadow-sm">
          {processedMessage}
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      "flex gap-3 mb-4",
      isOwn ? "flex-row-reverse" : "flex-row"
    )}>
      {!isOwn && (
        <Avatar className="w-9 h-9 mt-1 border-2 border-white shadow-md">
          <AvatarImage src={senderAvatar} />
          <AvatarFallback className="text-xs bg-mxit-blue-500 text-white font-semibold">
            {senderName?.charAt(0).toUpperCase() || 'U'}
          </AvatarFallback>
        </Avatar>
      )}

      <div className={cn(
        "flex flex-col max-w-[75%] md:max-w-[70%]",
        isOwn ? "items-end" : "items-start"
      )}>
        {!isOwn && senderName && (
          <span className="text-xs text-mxit-blue-600 dark:text-mxit-blue-400 font-semibold mb-1 px-2">
            {senderName}
          </span>
        )}

        <div className={cn(
          "px-4 py-3 rounded-2xl shadow-md break-words relative",
          isOwn
            ? "bg-gradient-to-r from-mxit-blue-500 to-mxit-blue-600 text-white rounded-br-md"
            : "bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-bl-md border border-gray-200 dark:border-gray-600"
        )}>
          <p className="text-sm leading-relaxed whitespace-pre-wrap">
            {processedMessage}
          </p>

          {/* Message tail */}
          <div className={cn(
            "absolute w-0 h-0 border-solid",
            isOwn
              ? "bottom-0 right-0 border-l-[8px] border-l-transparent border-t-[8px] border-t-mxit-blue-600"
              : "bottom-0 left-0 border-r-[8px] border-r-transparent border-t-[8px] border-t-white dark:border-t-gray-700"
          )} />
        </div>

        <span className={cn(
          "text-xs text-gray-500 dark:text-gray-400 mt-2 px-2",
          isOwn ? "text-right" : "text-left"
        )}>
          {formatTime(timestamp)}
        </span>
      </div>
    </div>
  )
}
