"use client"

import { useState, useRef, KeyboardEvent } from "react"
import { Send, Smile, Paperclip, Mic } from "lucide-react"
import { cn } from "@/lib/utils"

interface ChatInputProps {
  onSendMessage: (message: string) => void
  placeholder?: string
  disabled?: boolean
  isMobile?: boolean
}

export function ChatInput({
  onSendMessage,
  placeholder = "Type a message...",
  disabled = false,
  isMobile = false
}: ChatInputProps) {
  const [message, setMessage] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const handleSend = () => {
    if (message.trim() && !disabled) {
      onSendMessage(message.trim())
      setMessage("")
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto'
      }
    }
  }

  const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value)
    
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`
    }
  }

  const insertEmoticon = (emoticon: string) => {
    setMessage(prev => prev + emoticon)
    textareaRef.current?.focus()
  }

  const commonEmoticons = [':)', ':(', ':D', ':P', ';)', ':o', ':*', '<3', ':@', '8)']

  return (
    <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4 shadow-lg">
      {/* Emoticon bar */}
      <div className="flex gap-2 mb-4 overflow-x-auto scrollbar-hide">
        {commonEmoticons.map((emoticon) => (
          <button
            key={emoticon}
            onClick={() => insertEmoticon(emoticon)}
            className="flex-shrink-0 px-3 py-2 bg-gradient-to-r from-mxit-blue-50 to-mxit-blue-100 dark:from-mxit-blue-900 dark:to-mxit-blue-800 hover:from-mxit-blue-100 hover:to-mxit-blue-200 dark:hover:from-mxit-blue-800 dark:hover:to-mxit-blue-700 text-mxit-blue-600 dark:text-mxit-blue-300 rounded-full text-sm font-medium transition-all shadow-sm"
          >
            {emoticon}
          </button>
        ))}
      </div>

      {/* Input area */}
      <div className="flex items-end gap-3">
        <button className="p-3 text-gray-500 dark:text-gray-400 hover:text-mxit-blue-500 dark:hover:text-mxit-blue-400 transition-colors rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
          <Paperclip className="w-5 h-5" />
        </button>

        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyDown={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              "w-full pr-14 border-2 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-mxit-blue-500 focus:border-mxit-blue-500 scrollbar-hide placeholder-gray-500 dark:placeholder-gray-400",
              isMobile ? "mobile-text px-4 py-3 min-h-[52px]" : "px-4 py-3 min-h-[52px]",
              "max-h-[120px] shadow-sm",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            rows={1}
          />
          
          <button
            onClick={() => setIsRecording(!isRecording)}
            className={cn(
              "absolute right-4 top-1/2 -translate-y-1/2 p-2 rounded-full transition-all",
              isRecording
                ? "text-red-500 bg-red-100 dark:bg-red-900 animate-pulse"
                : "text-gray-500 dark:text-gray-400 hover:text-mxit-blue-500 dark:hover:text-mxit-blue-400 hover:bg-gray-100 dark:hover:bg-gray-600"
            )}
          >
            <Mic className="w-4 h-4" />
          </button>
        </div>

        <button
          onClick={handleSend}
          disabled={!message.trim() || disabled}
          className={cn(
            "p-3 rounded-full transition-all duration-200 shadow-lg",
            message.trim() && !disabled
              ? "bg-gradient-to-r from-mxit-blue-500 to-mxit-blue-600 text-white hover:from-mxit-blue-600 hover:to-mxit-blue-700 hover:shadow-xl transform hover:scale-105"
              : "bg-gray-200 dark:bg-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed"
          )}
        >
          <Send className="w-5 h-5" />
        </button>
      </div>

      {/* Typing indicator area */}
      <div className="mt-3 h-4 flex items-center">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-mxit-blue-400 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-mxit-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
          <div className="w-2 h-2 bg-mxit-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
        </div>
        <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">Someone is typing...</span>
      </div>
    </div>
  )
}
