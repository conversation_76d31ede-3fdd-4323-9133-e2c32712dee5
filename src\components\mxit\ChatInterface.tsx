"use client"

import { useState, useEffect, useRef } from "react"
import { ContactList } from "./ContactList"
import { ChatBubble } from "./ChatBubble"
import { ChatInput } from "./ChatInput"
import { MxitGames } from "@/components/games/MxitGames"
import { RoomDiscovery } from "@/components/rooms/RoomDiscovery"
import { FileSharing } from "@/components/files/FileSharing"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Phone, Video, MoreVertical, Menu } from "lucide-react"
import { cn } from "@/lib/utils"

interface Message {
  id: string
  content: string
  timestamp: Date
  senderId: string
  senderName?: string
  senderAvatar?: string
  isSystem?: boolean
}

interface Contact {
  id: string
  name: string
  nickname?: string
  avatar?: string
  status: 'online' | 'away' | 'busy' | 'offline'
  mood?: string
  lastMessage?: string
  lastSeen?: Date
  unreadCount?: number
}

interface ChatInterfaceProps {
  currentUserId: string
  isMobile?: boolean
}

export function ChatInterface({ currentUserId, isMobile = false }: ChatInterfaceProps) {
  const [selectedContactId, setSelectedContactId] = useState<string>()
  const [messages, setMessages] = useState<Message[]>([])
  const [showContactList, setShowContactList] = useState(!isMobile)
  const [showGames, setShowGames] = useState(false)
  const [showRooms, setShowRooms] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [showFileSharing, setShowFileSharing] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Mock data - in real app this would come from Convex
  const mockContacts: Contact[] = [
    {
      id: "1",
      name: "Thabo Mthembu",
      nickname: "T-Money",
      status: "online",
      mood: "Living my best life! 🔥",
      lastMessage: "Eish bru, you coming to the braai?",
      unreadCount: 2
    },
    {
      id: "2", 
      name: "Nomsa Dlamini",
      nickname: "Noms",
      status: "away",
      mood: "At work, holla later",
      lastMessage: "Sharp sharp, catch you later!",
      unreadCount: 0
    },
    {
      id: "3",
      name: "Sipho Khumalo", 
      nickname: "Sips",
      status: "busy",
      mood: "Studying for exams 📚",
      lastMessage: "Can't chat now, writing tomorrow",
      unreadCount: 1
    }
  ]

  const mockMessages: Message[] = [
    {
      id: "1",
      content: "Howzit bru! How's things?",
      timestamp: new Date(Date.now() - 3600000),
      senderId: "1",
      senderName: "T-Money"
    },
    {
      id: "2", 
      content: "Eish, all good my side! Just chilling at home. You?",
      timestamp: new Date(Date.now() - 3500000),
      senderId: currentUserId
    },
    {
      id: "3",
      content: "Same same. Thinking of having a braai this weekend. You keen? :D",
      timestamp: new Date(Date.now() - 3400000),
      senderId: "1",
      senderName: "T-Money"
    },
    {
      id: "4",
      content: "Definitely! Count me in. Should I bring some boerewors?",
      timestamp: new Date(Date.now() - 3300000),
      senderId: currentUserId
    },
    {
      id: "5",
      content: "T-Money has changed their mood to: Ready for the weekend! 🍖🔥",
      timestamp: new Date(Date.now() - 3200000),
      senderId: "system",
      isSystem: true
    }
  ]

  useEffect(() => {
    if (selectedContactId) {
      setMessages(mockMessages)
    }
  }, [selectedContactId])

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const handleContactSelect = (contactId: string) => {
    setSelectedContactId(contactId)
    if (isMobile) {
      setShowContactList(false)
    }
  }

  const handleSendMessage = (content: string) => {
    const newMessage: Message = {
      id: Date.now().toString(),
      content,
      timestamp: new Date(),
      senderId: currentUserId
    }
    setMessages(prev => [...prev, newMessage])
  }

  const handleStartGame = (gameId: string) => {
    setShowGames(false)
    // TODO: Implement game start logic
    console.log("Starting game:", gameId)
  }

  const handleJoinRoom = (roomId: string) => {
    setShowRooms(false)
    // TODO: Implement room join logic
    console.log("Joining room:", roomId)
  }

  const handleCreateRoom = () => {
    setShowRooms(false)
    // TODO: Implement room creation logic
    console.log("Creating new room")
  }

  const handleFileUpload = (file: File) => {
    setShowFileSharing(false)
    // TODO: Implement file upload logic
    console.log("Uploading file:", file.name)
  }

  const handleFileDownload = (fileId: string) => {
    // TODO: Implement file download logic
    console.log("Downloading file:", fileId)
  }

  const selectedContact = mockContacts.find(c => c.id === selectedContactId)

  return (
    <div className="h-screen flex bg-gray-50 dark:bg-gray-900 overflow-hidden">
      {/* Contact List */}
      <div className={cn(
        "bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 flex flex-col shadow-lg",
        isMobile
          ? showContactList
            ? "fixed inset-0 z-40 w-full"
            : "hidden"
          : "w-80 flex-shrink-0"
      )}>
        <ContactList
          contacts={mockContacts}
          onContactSelect={handleContactSelect}
          selectedContactId={selectedContactId}
          onShowGames={() => setShowGames(true)}
          onShowRooms={() => setShowRooms(true)}
          onShowNotifications={() => setShowNotifications(true)}
          onShowFileSharing={() => setShowFileSharing(true)}
          onClose={() => setShowContactList(false)}
          isMobile={isMobile}
        />
      </div>

      {/* Chat Area */}
      <div className={cn(
        "flex-1 flex flex-col bg-white dark:bg-gray-800",
        isMobile && showContactList && "hidden"
      )}>
        {selectedContact ? (
          <>
            {/* Chat Header */}
            <div className="bg-gradient-to-r from-mxit-blue-500 to-mxit-blue-600 dark:from-mxit-blue-600 dark:to-mxit-blue-700 p-4 text-white shadow-lg border-b border-mxit-blue-400">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {isMobile && (
                    <button
                      onClick={() => setShowContactList(true)}
                      className="p-2 hover:bg-white/20 rounded-lg transition-colors"
                    >
                      <Menu className="w-5 h-5" />
                    </button>
                  )}
                  <Avatar className="w-10 h-10 border-2 border-white/20 shadow-md">
                    <AvatarImage src={selectedContact.avatar} />
                    <AvatarFallback className="bg-mxit-orange-500 text-white font-semibold">
                      {(selectedContact.nickname || selectedContact.name).slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h2 className="font-semibold text-lg">{selectedContact.nickname || selectedContact.name}</h2>
                    <div className="flex items-center space-x-2">
                      <div className={cn(
                        "w-2 h-2 rounded-full",
                        selectedContact.status === 'online' ? 'bg-green-400' :
                        selectedContact.status === 'away' ? 'bg-yellow-400' :
                        selectedContact.status === 'busy' ? 'bg-red-400' : 'bg-gray-400'
                      )} />
                      <p className="text-sm text-mxit-blue-100">{selectedContact.mood || selectedContact.status}</p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <button className="p-2 hover:bg-white/20 rounded-lg transition-colors">
                    <Phone className="w-5 h-5" />
                  </button>
                  <button className="p-2 hover:bg-white/20 rounded-lg transition-colors">
                    <Video className="w-5 h-5" />
                  </button>
                  <button className="p-2 hover:bg-white/20 rounded-lg transition-colors">
                    <MoreVertical className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 space-y-3">
              {messages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center">
                  <div className="w-16 h-16 bg-mxit-blue-100 dark:bg-mxit-blue-900 rounded-full flex items-center justify-center mb-4">
                    <span className="text-2xl">💬</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">
                    Start a conversation
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 max-w-sm">
                    Send a message to {selectedContact.nickname || selectedContact.name} to get the conversation started!
                  </p>
                </div>
              ) : (
                <>
                  {messages.map((message) => (
                    <ChatBubble
                      key={message.id}
                      message={message.content}
                      timestamp={message.timestamp}
                      isOwn={message.senderId === currentUserId}
                      senderName={message.senderName}
                      senderAvatar={message.senderAvatar}
                      isSystem={message.isSystem}
                    />
                  ))}
                  <div ref={messagesEndRef} />
                </>
              )}
            </div>

            {/* Chat Input */}
            <ChatInput onSendMessage={handleSendMessage} isMobile={isMobile} />
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
            <div className="text-center p-8 max-w-md">
              <div className="w-32 h-32 mx-auto mb-6 bg-gradient-to-br from-mxit-blue-500 to-mxit-blue-600 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-5xl">🇿🇦</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                Welcome to XitChats!
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                Feel the Mxit vibe again! Select a contact to start chatting, join rooms, or play games.
                <br />
                <span className="text-mxit-blue-600 dark:text-mxit-blue-400 font-medium">Sharp sharp, let's chat! 💬</span>
              </p>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="bg-white dark:bg-gray-700 p-3 rounded-lg shadow-sm">
                  <span className="text-lg mb-1 block">💬</span>
                  <span className="text-gray-700 dark:text-gray-300">Chat with friends</span>
                </div>
                <div className="bg-white dark:bg-gray-700 p-3 rounded-lg shadow-sm">
                  <span className="text-lg mb-1 block">🎮</span>
                  <span className="text-gray-700 dark:text-gray-300">Play games</span>
                </div>
                <div className="bg-white dark:bg-gray-700 p-3 rounded-lg shadow-sm">
                  <span className="text-lg mb-1 block">#️⃣</span>
                  <span className="text-gray-700 dark:text-gray-300">Join rooms</span>
                </div>
                <div className="bg-white dark:bg-gray-700 p-3 rounded-lg shadow-sm">
                  <span className="text-lg mb-1 block">📁</span>
                  <span className="text-gray-700 dark:text-gray-300">Share files</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      {showGames && (
        <MxitGames
          onStartGame={handleStartGame}
          onClose={() => setShowGames(false)}
        />
      )}

      {showRooms && (
        <RoomDiscovery
          onJoinRoom={handleJoinRoom}
          onCreateRoom={handleCreateRoom}
          onClose={() => setShowRooms(false)}
        />
      )}

      {showNotifications && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Notifications</h3>
              <button
                onClick={() => setShowNotifications(false)}
                className="w-6 h-6 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center"
              >
                ✕
              </button>
            </div>
            <div className="space-y-3">
              <div className="p-3 bg-mxit-blue-50 rounded-lg">
                <p className="text-sm font-medium text-mxit-blue-800">T-Money started a game!</p>
                <p className="text-xs text-mxit-blue-600">Join the Mzansi Trivia challenge</p>
              </div>
              <div className="p-3 bg-mxit-orange-50 rounded-lg">
                <p className="text-sm font-medium text-mxit-orange-800">New room: Cape Town Locals</p>
                <p className="text-xs text-mxit-orange-600">23 people are chatting about local events</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <p className="text-sm font-medium text-green-800">Noms changed mood</p>
                <p className="text-xs text-green-600">"Ready for the weekend! 🎉"</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {showFileSharing && (
        <FileSharing
          onFileUpload={handleFileUpload}
          onFileDownload={handleFileDownload}
          onClose={() => setShowFileSharing(false)}
        />
      )}
    </div>
  )
}
