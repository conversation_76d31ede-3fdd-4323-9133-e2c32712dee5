"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import { MessageCircle, Settings, Gamepad2, Hash, Bell, Upload, X } from "lucide-react"

interface Contact {
  id: string
  name: string
  nickname?: string
  avatar?: string
  status: 'online' | 'away' | 'busy' | 'offline'
  mood?: string
  lastMessage?: string
  lastSeen?: Date
  unreadCount?: number
}

interface ContactListProps {
  contacts: Contact[]
  onContactSelect: (contactId: string) => void
  selectedContactId?: string
  onShowGames?: () => void
  onShowRooms?: () => void
  onShowNotifications?: () => void
  onShowFileSharing?: () => void
  onClose?: () => void
  isMobile?: boolean
}

export function ContactList({
  contacts,
  onContactSelect,
  selectedContactId,
  onShowGames,
  onShowRooms,
  onShowNotifications,
  onShowFileSharing,
  onClose,
  isMobile = false
}: ContactListProps) {
  const getStatusColor = (status: Contact['status']) => {
    switch (status) {
      case 'online': return 'bg-mxit-status-online'
      case 'away': return 'bg-mxit-status-away'
      case 'busy': return 'bg-mxit-status-busy'
      default: return 'bg-mxit-status-offline'
    }
  }

  return (
    <div className="h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
      {/* Header */}
      <div className="bg-gradient-to-r from-mxit-blue-500 to-mxit-blue-600 dark:from-mxit-blue-600 dark:to-mxit-blue-700 p-4 text-white shadow-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <span className="text-lg font-bold">X</span>
            </div>
            <h1 className="text-xl font-bold">XitChats</h1>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={onShowNotifications}
              className="relative p-2 hover:bg-white/20 rounded-lg transition-colors"
            >
              <Bell className="w-5 h-5" />
              <span className="absolute -top-1 -right-1 w-4 h-4 bg-mxit-orange-500 rounded-full text-xs flex items-center justify-center font-semibold">
                3
              </span>
            </button>
            {isMobile && onClose && (
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            )}
            <button className="p-2 hover:bg-white/20 rounded-lg transition-colors">
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
        <p className="text-mxit-blue-100 text-sm mt-2">
          Welcome back to the vibe! 🇿🇦
        </p>
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-b border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-750">
        <div className="grid grid-cols-2 gap-3 mb-3">
          <button className="flex items-center justify-center gap-2 py-3 px-3 bg-gradient-to-r from-mxit-orange-500 to-mxit-orange-600 text-white rounded-lg hover:from-mxit-orange-600 hover:to-mxit-orange-700 transition-all shadow-sm">
            <MessageCircle className="w-4 h-4" />
            <span className="text-sm font-medium">New Chat</span>
          </button>
          <button
            onClick={onShowRooms}
            className="flex items-center justify-center gap-2 py-3 px-3 bg-gradient-to-r from-mxit-blue-500 to-mxit-blue-600 text-white rounded-lg hover:from-mxit-blue-600 hover:to-mxit-blue-700 transition-all shadow-sm"
          >
            <Hash className="w-4 h-4" />
            <span className="text-sm font-medium">Rooms</span>
          </button>
        </div>
        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={onShowGames}
            className="flex items-center justify-center gap-2 py-2 px-3 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors"
          >
            <Gamepad2 className="w-4 h-4" />
            <span className="text-sm font-medium">Games</span>
          </button>
          <button
            onClick={onShowFileSharing}
            className="flex items-center justify-center gap-2 py-2 px-3 bg-purple-50 text-purple-600 rounded-lg hover:bg-purple-100 transition-colors"
          >
            <Upload className="w-4 h-4" />
            <span className="text-sm font-medium">Files</span>
          </button>
        </div>
      </div>

      {/* Contacts */}
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        {contacts.map((contact) => (
          <div
            key={contact.id}
            onClick={() => onContactSelect(contact.id)}
            className={cn(
              "border-b border-gray-50 cursor-pointer hover:bg-mxit-blue-25 transition-colors",
              isMobile ? "mobile-contact-item" : "p-3",
              selectedContactId === contact.id && "bg-mxit-blue-50 border-l-4 border-l-mxit-blue-500"
            )}
          >
            <div className="flex items-center gap-3">
              <div className="relative">
                <Avatar className="w-12 h-12">
                  <AvatarImage src={contact.avatar} />
                  <AvatarFallback>
                    {(contact.nickname || contact.name).charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className={cn(
                  "absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white",
                  getStatusColor(contact.status)
                )} />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-gray-900 truncate">
                    {contact.nickname || contact.name}
                  </h3>
                  {contact.unreadCount && contact.unreadCount > 0 && (
                    <span className="bg-mxit-orange-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                      {contact.unreadCount > 99 ? '99+' : contact.unreadCount}
                    </span>
                  )}
                </div>
                
                {contact.mood && (
                  <p className="text-xs text-mxit-blue-600 italic truncate mt-1">
                    {contact.mood}
                  </p>
                )}
                
                {contact.lastMessage && (
                  <p className="text-sm text-gray-600 truncate mt-1">
                    {contact.lastMessage}
                  </p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-200 bg-gray-50">
        <p className="text-xs text-center text-gray-500">
          Made with ❤️ for Mzansi
        </p>
      </div>
    </div>
  )
}
