"use client"

import { useState } from "react"
import { useUser } from "@clerk/nextjs"
import { useMutation } from "convex/react"
import { api } from "../../../convex/_generated/api"
import { useRouter } from "next/navigation"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MapPin, Heart, Smile, ArrowRight, Check } from "lucide-react"

const SA_PROVINCES = [
  "Western Cape", "Eastern Cape", "Northern Cape", "Free State",
  "KwaZulu-Natal", "North West", "Gauteng", "Mpumalanga", "Limpopo"
]

const SA_LANGUAGES = [
  "English", "Afrikaans", "isiZulu", "isiXhosa", "Sepedi", "Setswana",
  "Sesotho", "isiNdebele", "SiSwati", "Tshivenda", "Xitsonga"
]

const MOOD_SUGGESTIONS = [
  "Living my best life! 🔥",
  "Chilling at home 🏠",
  "Ready for the weekend! 🎉",
  "Braai time! 🍖",
  "Studying hard 📚",
  "Work mode activated 💼",
  "Feeling blessed 🙏",
  "Rugby season! 🏉",
  "Sunshine vibes ☀️",
  "Lekker day ahead! 👌"
]

const FAVORITE_EMOJIS = [
  "😊", "😎", "🔥", "❤️", "👌", "🙌", "😂", "🤔", "😍", "🎉",
  "🍖", "🏉", "☀️", "🇿🇦", "💎", "🦁", "🌊", "🏔️", "🌺", "⚡"
]

export function XitOnboarding() {
  const { user } = useUser()
  const router = useRouter()
  const createUser = useMutation(api.users.createUser)
  
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    nickname: "",
    location: "",
    language: "",
    mood: "",
    favoriteEmoji: "😊",
    aboutMe: ""
  })

  const handleNext = () => {
    if (step < 4) {
      setStep(step + 1)
    } else {
      handleComplete()
    }
  }

  const handleComplete = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      await createUser({
        clerkId: user.id,
        email: user.emailAddresses[0]?.emailAddress || "",
        username: user.username || user.firstName || "user",
        nickname: formData.nickname || user.firstName || "New User",
        avatar: user.imageUrl,
      })

      // TODO: Update additional profile info with separate mutation
      
      router.push("/chat")
    } catch (error) {
      console.error("Error creating user:", error)
    } finally {
      setLoading(false)
    }
  }

  const isStepComplete = () => {
    switch (step) {
      case 1: return formData.nickname.length > 0
      case 2: return formData.location.length > 0 && formData.language.length > 0
      case 3: return formData.mood.length > 0
      case 4: return true
      default: return false
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-mxit-blue-500 to-mxit-orange-500 flex items-center justify-center p-4">
      <div className="bg-white rounded-3xl shadow-2xl p-8 w-full max-w-2xl">
        {/* Progress bar */}
        <div className="flex items-center justify-between mb-8">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold ${
                i <= step ? 'bg-mxit-blue-500 text-white' : 'bg-gray-200 text-gray-500'
              }`}>
                {i < step ? <Check className="w-5 h-5" /> : i}
              </div>
              {i < 4 && (
                <div className={`w-16 h-1 mx-2 ${
                  i < step ? 'bg-mxit-blue-500' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step 1: Basic Info */}
        {step === 1 && (
          <div className="text-center space-y-6">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Welcome to XitChat!
              </h2>
              <p className="text-gray-600">
                Let's set up your profile to get the full XitChat experience
              </p>
            </div>

            <div className="flex justify-center">
              <Avatar className="w-24 h-24">
                <AvatarImage src={user?.imageUrl} />
                <AvatarFallback className="text-2xl">
                  {user?.firstName?.charAt(0) || "U"}
                </AvatarFallback>
              </Avatar>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Choose your nickname (what friends will see)
                </label>
                <input
                  type="text"
                  value={formData.nickname}
                  onChange={(e) => setFormData({...formData, nickname: e.target.value})}
                  placeholder="e.g., T-Money, Noms, Sips..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-mxit-blue-500 focus:border-transparent"
                  maxLength={20}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Make it memorable! This is how your friends will recognize you.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Step 2: Location & Language */}
        {step === 2 && (
          <div className="space-y-6">
            <div className="text-center">
              <MapPin className="w-12 h-12 text-mxit-blue-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Where are you from?
              </h2>
              <p className="text-gray-600">
                Help us connect you with people in your area
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Province
                </label>
                <select
                  value={formData.location}
                  onChange={(e) => setFormData({...formData, location: e.target.value})}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-mxit-blue-500 focus:border-transparent"
                >
                  <option value="">Select your province</option>
                  {SA_PROVINCES.map(province => (
                    <option key={province} value={province}>{province}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preferred Language
                </label>
                <select
                  value={formData.language}
                  onChange={(e) => setFormData({...formData, language: e.target.value})}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-mxit-blue-500 focus:border-transparent"
                >
                  <option value="">Select your language</option>
                  {SA_LANGUAGES.map(language => (
                    <option key={language} value={language}>{language}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Mood & Emoji */}
        {step === 3 && (
          <div className="space-y-6">
            <div className="text-center">
              <Heart className="w-12 h-12 text-mxit-orange-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Set your vibe!
              </h2>
              <p className="text-gray-600">
                Choose a mood message and favorite emoji
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mood Message
              </label>
              <input
                type="text"
                value={formData.mood}
                onChange={(e) => setFormData({...formData, mood: e.target.value})}
                placeholder="How are you feeling today?"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-mxit-blue-500 focus:border-transparent mb-3"
                maxLength={50}
              />
              
              <div className="grid grid-cols-2 gap-2">
                {MOOD_SUGGESTIONS.slice(0, 6).map(mood => (
                  <button
                    key={mood}
                    onClick={() => setFormData({...formData, mood})}
                    className="text-left px-3 py-2 text-sm bg-gray-50 hover:bg-mxit-blue-50 rounded-lg transition-colors"
                  >
                    {mood}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Favorite Emoji
              </label>
              <div className="grid grid-cols-10 gap-2">
                {FAVORITE_EMOJIS.map(emoji => (
                  <button
                    key={emoji}
                    onClick={() => setFormData({...formData, favoriteEmoji: emoji})}
                    className={`w-10 h-10 text-xl rounded-lg border-2 transition-colors ${
                      formData.favoriteEmoji === emoji 
                        ? 'border-mxit-blue-500 bg-mxit-blue-50' 
                        : 'border-gray-200 hover:border-mxit-blue-300'
                    }`}
                  >
                    {emoji}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Step 4: Final */}
        {step === 4 && (
          <div className="text-center space-y-6">
            <div>
              <Smile className="w-16 h-16 text-mxit-orange-500 mx-auto mb-4" />
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                You're all set! 🎉
              </h2>
              <p className="text-gray-600">
                Ready to start chatting and reliving those Mxit memories?
              </p>
            </div>

            <div className="bg-mxit-blue-50 rounded-2xl p-6">
              <h3 className="font-semibold text-gray-900 mb-3">Your Profile:</h3>
              <div className="space-y-2 text-sm text-gray-700">
                <p><strong>Nickname:</strong> {formData.nickname}</p>
                <p><strong>Location:</strong> {formData.location}</p>
                <p><strong>Language:</strong> {formData.language}</p>
                <p><strong>Mood:</strong> {formData.mood}</p>
                <p><strong>Favorite Emoji:</strong> {formData.favoriteEmoji}</p>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <button
            onClick={() => setStep(Math.max(1, step - 1))}
            disabled={step === 1}
            className="px-6 py-3 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Back
          </button>

          <button
            onClick={handleNext}
            disabled={!isStepComplete() || loading}
            className="flex items-center gap-2 px-6 py-3 bg-mxit-blue-500 text-white rounded-lg hover:bg-mxit-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? "Setting up..." : step === 4 ? "Start Chatting!" : "Next"}
            {!loading && <ArrowRight className="w-4 h-4" />}
          </button>
        </div>
      </div>
    </div>
  )
}
