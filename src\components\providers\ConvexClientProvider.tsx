"use client";

import { ReactNode, useEffect, useState } from "react";
import { ConvexProvider, ConvexReactClient } from "convex/react";

export function ConvexClientProvider({ children }: { children: ReactNode }) {
  const [client, setClient] = useState<ConvexReactClient | null>(null);

  useEffect(() => {
    // Dynamically import the Convex client to avoid SSR issues
    const setupClient = async () => {
      try {
        const convexUrl = process.env.NEXT_PUBLIC_CONVEX_URL;
        if (!convexUrl) {
          throw new Error("NEXT_PUBLIC_CONVEX_URL is not set");
        }
        const { ConvexReactClient } = await import("convex/react");
        setClient(new ConvexReactClient(convexUrl));
      } catch (error) {
        console.error("Failed to initialize Convex client:", error);
      }
    };

    setupClient();
  }, []);

  if (!client) {
    // You can return a loading state here if needed
    return <>{children}</>;
  }

  return <ConvexProvider client={client}>{children}</ConvexProvider>;
}
