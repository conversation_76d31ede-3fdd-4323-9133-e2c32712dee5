"use client"

import { useState } from "react"
import { <PERSON>, Hash, MapPin, Clock, Star, Plus, Search } from "lucide-react"
import { cn } from "@/lib/utils"

interface Room {
  id: string
  name: string
  description: string
  category: string
  memberCount: number
  maxMembers: number
  isActive: boolean
  lastActivity: Date
  creator: string
  tags: string[]
  language?: string
  location?: string
}

const SA_ROOMS: Room[] = [
  {
    id: "general-sa",
    name: "🇿🇦 General SA Chat",
    description: "Main hangout for all South Africans. Howzit everyone!",
    category: "general",
    memberCount: 247,
    maxMembers: 500,
    isActive: true,
    lastActivity: new Date(Date.now() - 2 * 60 * 1000),
    creator: "Admin",
    tags: ["general", "sa", "friendly"],
    language: "English"
  },
  {
    id: "braai-masters",
    name: "🍖 Braai Masters",
    description: "Share your braai tips, recipes, and stories. Lekker vibes only!",
    category: "lifestyle",
    memberCount: 89,
    maxMembers: 200,
    isActive: true,
    lastActivity: new Date(Date.now() - 15 * 60 * 1000),
    creator: "BraaiKing",
    tags: ["braai", "food", "recipes", "weekend"],
    language: "English"
  },
  {
    id: "springboks-fans",
    name: "🏉 Springboks Forever",
    description: "Rugby talk, match discussions, and Bok pride! Go Bokke!",
    category: "sports",
    memberCount: 156,
    maxMembers: 300,
    isActive: true,
    lastActivity: new Date(Date.now() - 5 * 60 * 1000),
    creator: "RugbyFan1995",
    tags: ["rugby", "springboks", "sports", "match-day"],
    language: "English"
  },
  {
    id: "cape-town-locals",
    name: "🏔️ Cape Town Locals",
    description: "Mother City residents and visitors. Share local tips and events!",
    category: "location",
    memberCount: 134,
    maxMembers: 250,
    isActive: true,
    lastActivity: new Date(Date.now() - 8 * 60 * 1000),
    creator: "CapeTownian",
    tags: ["cape-town", "local", "events", "tips"],
    location: "Western Cape"
  },
  {
    id: "joburg-vibes",
    name: "💎 Joburg Vibes",
    description: "Egoli crew! Share what's happening in the City of Gold.",
    category: "location",
    memberCount: 98,
    maxMembers: 200,
    isActive: true,
    lastActivity: new Date(Date.now() - 12 * 60 * 1000),
    creator: "JoziFan",
    tags: ["johannesburg", "joburg", "gauteng", "city-life"],
    location: "Gauteng"
  },
  {
    id: "afrikaans-chat",
    name: "🗣️ Afrikaans Gesels",
    description: "Praat Afrikaans hier! Lekker plek om te gesels en vriende te maak.",
    category: "language",
    memberCount: 67,
    maxMembers: 150,
    isActive: true,
    lastActivity: new Date(Date.now() - 20 * 60 * 1000),
    creator: "TaalLiefhebber",
    tags: ["afrikaans", "taal", "kultuur"],
    language: "Afrikaans"
  },
  {
    id: "university-students",
    name: "🎓 SA University Students",
    description: "Students from all SA universities. Study tips, campus life, and more!",
    category: "education",
    memberCount: 203,
    maxMembers: 400,
    isActive: true,
    lastActivity: new Date(Date.now() - 3 * 60 * 1000),
    creator: "StudyBuddy",
    tags: ["university", "students", "study", "campus"],
    language: "English"
  },
  {
    id: "music-lovers",
    name: "🎵 SA Music Lovers",
    description: "From kwaito to amapiano, rock to gospel. All SA music welcome!",
    category: "entertainment",
    memberCount: 112,
    maxMembers: 250,
    isActive: true,
    lastActivity: new Date(Date.now() - 7 * 60 * 1000),
    creator: "MusicMaestro",
    tags: ["music", "amapiano", "kwaito", "local-music"],
    language: "English"
  }
]

const CATEGORIES = [
  { id: "all", name: "All Rooms", icon: "🌍", color: "bg-gray-100 text-gray-700" },
  { id: "general", name: "General", icon: "💬", color: "bg-blue-100 text-blue-700" },
  { id: "sports", name: "Sports", icon: "🏉", color: "bg-green-100 text-green-700" },
  { id: "lifestyle", name: "Lifestyle", icon: "🍖", color: "bg-orange-100 text-orange-700" },
  { id: "location", name: "Local", icon: "📍", color: "bg-purple-100 text-purple-700" },
  { id: "language", name: "Language", icon: "🗣️", color: "bg-pink-100 text-pink-700" },
  { id: "education", name: "Education", icon: "🎓", color: "bg-indigo-100 text-indigo-700" },
  { id: "entertainment", name: "Entertainment", icon: "🎵", color: "bg-yellow-100 text-yellow-700" },
]

interface RoomDiscoveryProps {
  onJoinRoom: (roomId: string) => void
  onCreateRoom: () => void
  onClose: () => void
}

export function RoomDiscovery({ onJoinRoom, onCreateRoom, onClose }: RoomDiscoveryProps) {
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")

  const filteredRooms = SA_ROOMS.filter(room => {
    const matchesCategory = selectedCategory === "all" || room.category === selectedCategory
    const matchesSearch = searchQuery === "" || 
      room.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      room.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      room.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    return matchesCategory && matchesSearch
  })

  const getTimeAgo = (date: Date) => {
    const minutes = Math.floor((Date.now() - date.getTime()) / (1000 * 60))
    if (minutes < 1) return "Just now"
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours}h ago`
    return `${Math.floor(hours / 24)}d ago`
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="mxit-gradient p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Hash className="w-8 h-8" />
              <div>
                <h2 className="text-2xl font-bold">Discover Rooms</h2>
                <p className="text-mxit-blue-100">Join conversations happening across Mzansi! 🇿🇦</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center transition-colors"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* Search and Create */}
          <div className="flex gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search rooms, topics, or locations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-mxit-blue-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={onCreateRoom}
              className="flex items-center gap-2 px-6 py-3 bg-mxit-orange-500 text-white rounded-lg hover:bg-mxit-orange-600 transition-colors"
            >
              <Plus className="w-5 h-5" />
              Create Room
            </button>
          </div>

          {/* Categories */}
          <div className="flex gap-2 mb-6 overflow-x-auto pb-2">
            {CATEGORIES.map(category => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors",
                  selectedCategory === category.id
                    ? "bg-mxit-blue-500 text-white"
                    : category.color
                )}
              >
                <span>{category.icon}</span>
                <span>{category.name}</span>
              </button>
            ))}
          </div>

          {/* Rooms Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[500px] overflow-y-auto">
            {filteredRooms.map(room => (
              <div
                key={room.id}
                className="border border-gray-200 rounded-xl p-4 hover:shadow-lg transition-all hover:border-mxit-blue-300"
              >
                <div className="flex items-start justify-between mb-3">
                  <h3 className="font-semibold text-gray-900 text-sm leading-tight">
                    {room.name}
                  </h3>
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Active</span>
                  </div>
                </div>

                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {room.description}
                </p>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <Users className="w-3 h-3" />
                      <span>{room.memberCount}/{room.maxMembers}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>{getTimeAgo(room.lastActivity)}</span>
                    </div>
                  </div>

                  {room.location && (
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <MapPin className="w-3 h-3" />
                      <span>{room.location}</span>
                    </div>
                  )}

                  <div className="flex flex-wrap gap-1">
                    {room.tags.slice(0, 3).map(tag => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>

                <button
                  onClick={() => onJoinRoom(room.id)}
                  className="w-full py-2 bg-mxit-blue-500 text-white rounded-lg hover:bg-mxit-blue-600 transition-colors text-sm font-medium"
                >
                  Join Room
                </button>
              </div>
            ))}
          </div>

          {filteredRooms.length === 0 && (
            <div className="text-center py-12">
              <Hash className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No rooms found</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery 
                  ? `No rooms match "${searchQuery}". Try a different search term.`
                  : "No rooms in this category yet."
                }
              </p>
              <button
                onClick={onCreateRoom}
                className="px-6 py-3 bg-mxit-blue-500 text-white rounded-lg hover:bg-mxit-blue-600 transition-colors"
              >
                Create the First Room
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
