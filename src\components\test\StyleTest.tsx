"use client"

export function StyleTest() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          XitChats Style Test
        </h1>
        
        {/* Test Mxit Colors */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Mxit Colors</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-mxit-blue-500 text-white p-4 rounded-lg text-center">
              Mxit Blue 500
            </div>
            <div className="bg-mxit-orange-500 text-white p-4 rounded-lg text-center">
              Mxit Orange 500
            </div>
            <div className="bg-mxit-status-online text-white p-4 rounded-lg text-center">
              Online Status
            </div>
            <div className="bg-mxit-status-away text-white p-4 rounded-lg text-center">
              Away Status
            </div>
          </div>
        </div>

        {/* Test Gradients */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Gradients</h2>
          <div className="mxit-gradient text-white p-6 rounded-lg">
            <h3 className="text-lg font-semibold">Mxit Gradient Header</h3>
            <p>This should have the classic Mxit blue gradient</p>
          </div>
          <div className="bg-gradient-to-r from-mxit-blue-500 to-mxit-blue-600 text-white p-6 rounded-lg">
            <h3 className="text-lg font-semibold">Tailwind Gradient</h3>
            <p>This uses Tailwind gradient classes</p>
          </div>
        </div>

        {/* Test Chat Bubbles */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Chat Bubbles</h2>
          <div className="space-y-3">
            <div className="flex justify-end">
              <div className="bg-mxit-blue-500 text-white px-4 py-2 rounded-2xl rounded-br-md max-w-xs">
                This is a sent message bubble
              </div>
            </div>
            <div className="flex justify-start">
              <div className="bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-2xl rounded-bl-md max-w-xs border border-gray-200 dark:border-gray-600">
                This is a received message bubble
              </div>
            </div>
          </div>
        </div>

        {/* Test Buttons */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Buttons</h2>
          <div className="flex flex-wrap gap-4">
            <button className="btn-primary">
              Primary Button
            </button>
            <button className="btn-secondary">
              Secondary Button
            </button>
            <button className="bg-gradient-to-r from-mxit-blue-500 to-mxit-blue-600 text-white px-4 py-2 rounded-lg hover:from-mxit-blue-600 hover:to-mxit-blue-700 transition-all">
              Gradient Button
            </button>
          </div>
        </div>

        {/* Test Status Indicators */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Status Indicators</h2>
          <div className="flex space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-mxit-status-online rounded-full"></div>
              <span>Online</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-mxit-status-away rounded-full"></div>
              <span>Away</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-mxit-status-busy rounded-full"></div>
              <span>Busy</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-mxit-status-offline rounded-full"></div>
              <span>Offline</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
