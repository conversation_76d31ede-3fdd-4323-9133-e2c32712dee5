// South African localized content and features

export const SA_SLANG = {
  // Common SA expressions
  'howzit': 'Hello, how are you?',
  'eish': 'Expression of frustration or sympathy',
  'lekker': 'Nice, good, awesome',
  'sharp': 'Cool, alright, okay',
  'braai': 'South African barbecue',
  'boerewors': 'Traditional SA sausage',
  'biltong': 'Dried meat snack',
  'bakkie': 'Pickup truck',
  'robot': 'Traffic light',
  'takkies': 'Sneakers/trainers',
  'cooldrink': 'Soft drink/soda',
  'sosatie': 'Kebab',
  'potjiekos': 'Traditional stew',
  'vetkoek': 'Fried bread',
  'koeksister': 'Traditional pastry',
  'rusk': 'Hard biscuit for dunking',
  'mielie': 'Corn/maize',
  'pap': 'Porridge/maize meal',
  'boet': 'Brother/friend',
  'china': 'Friend/mate',
  'ou toppie': 'Old man',
  'tannie': 'Aunt/older woman',
  'oom': 'Uncle/older man',
  'sarmie': 'Sandwich',
  'jol': 'Party/have fun',
  'skief': 'Crooked/wonky',
  'skelmpie': 'Rascal/naughty person',
  'tjommie': 'Friend/buddy',
  'shame': 'Expression of sympathy',
  'ag man': 'Oh man/expression of frustration',
  'just now': 'Later/in a while',
  'now now': 'Very soon',
  'is it': 'Really?/Is that so?',
  'hectic': 'Intense/crazy',
  'kak': 'Bad/rubbish',
  'lank': 'Very/a lot',
  'chommie': 'Friend',
  'bru': 'Brother/friend',
  'boytjie': 'Little boy/young man',
  'girlie': 'Girl/young woman',
}

export const SA_PROVINCES = [
  { name: 'Western Cape', capital: 'Cape Town', nickname: 'Mother City Province' },
  { name: 'Eastern Cape', capital: 'Bhisho', nickname: 'Frontier Country' },
  { name: 'Northern Cape', capital: 'Kimberley', nickname: 'Diamond Province' },
  { name: 'Free State', capital: 'Bloemfontein', nickname: 'Heart of SA' },
  { name: 'KwaZulu-Natal', capital: 'Pietermaritzburg', nickname: 'Garden Province' },
  { name: 'North West', capital: 'Mahikeng', nickname: 'Platinum Province' },
  { name: 'Gauteng', capital: 'Johannesburg', nickname: 'City of Gold Province' },
  { name: 'Mpumalanga', capital: 'Mbombela', nickname: 'Paradise Province' },
  { name: 'Limpopo', capital: 'Polokwane', nickname: 'Great North' },
]

export const SA_LANGUAGES = [
  { name: 'English', speakers: '9.6%', greeting: 'Hello' },
  { name: 'Afrikaans', speakers: '13.5%', greeting: 'Hallo' },
  { name: 'isiZulu', speakers: '22.7%', greeting: 'Sawubona' },
  { name: 'isiXhosa', speakers: '16.0%', greeting: 'Molo' },
  { name: 'Sepedi', speakers: '9.1%', greeting: 'Dumela' },
  { name: 'Setswana', speakers: '8.0%', greeting: 'Dumela' },
  { name: 'Sesotho', speakers: '7.6%', greeting: 'Dumela' },
  { name: 'isiNdebele', speakers: '2.1%', greeting: 'Lotjhani' },
  { name: 'SiSwati', speakers: '2.5%', greeting: 'Sawubona' },
  { name: 'Tshivenda', speakers: '2.4%', greeting: 'Ndaa' },
  { name: 'Xitsonga', speakers: '4.5%', greeting: 'Avuxeni' },
]

export const SA_FOOD_EMOJIS = {
  'braai': '🍖',
  'boerewors': '🌭',
  'biltong': '🥩',
  'sosatie': '🍢',
  'potjiekos': '🍲',
  'vetkoek': '🍞',
  'koeksister': '🥨',
  'rusk': '🍪',
  'mielie': '🌽',
  'pap': '🥣',
  'sarmie': '🥪',
  'cooldrink': '🥤',
}

export const SA_SPORTS_TEAMS = {
  rugby: [
    'Springboks', 'Bulls', 'Stormers', 'Sharks', 'Lions', 'Cheetahs'
  ],
  soccer: [
    'Kaizer Chiefs', 'Orlando Pirates', 'Mamelodi Sundowns', 'SuperSport United'
  ],
  cricket: [
    'Proteas', 'Lions', 'Titans', 'Warriors', 'Dolphins', 'Cobras'
  ]
}

export const SA_UNIVERSITIES = [
  'UCT', 'Wits', 'Stellenbosch', 'UP', 'UJ', 'UKZN', 'Rhodes', 'UFS',
  'NWU', 'UL', 'UWC', 'CPUT', 'TUT', 'VUT', 'DUT', 'NMMU'
]

export const SA_CITIES = [
  'Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth',
  'Bloemfontein', 'East London', 'Pietermaritzburg', 'Kimberley',
  'Polokwane', 'Nelspruit', 'Mahikeng', 'Upington', 'George',
  'Knysna', 'Hermanus', 'Stellenbosch', 'Paarl', 'Worcester',
  'Oudtshoorn', 'Mossel Bay', 'Plettenberg Bay'
]

export const SA_MOOD_SUGGESTIONS = [
  "Living my best life! 🔥",
  "Chilling at home 🏠", 
  "Ready for the weekend! 🎉",
  "Braai time! 🍖",
  "Studying hard 📚",
  "Work mode activated 💼",
  "Feeling blessed 🙏",
  "Rugby season! 🏉",
  "Sunshine vibes ☀️",
  "Lekker day ahead! 👌",
  "Eish, Monday blues 😅",
  "Sharp sharp! 👍",
  "Howzit going? 👋",
  "Jol time tonight! 🎊",
  "Missing the braai 🍖",
  "Biltong cravings 🥩",
  "Traffic robot broken again 🚦",
  "Load shedding vibes 💡",
  "Proudly South African 🇿🇦",
  "Mzansi magic ✨",
  "Ubuntu spirit 🤝",
  "Vuvuzela ready! 📯",
  "Boerewors on the fire 🌭",
  "Rooibos tea time ☕",
  "Springbok supporter 🏉",
  "Table Mountain views 🏔️",
  "Kruger Park dreams 🦁",
  "Garden Route calling 🌊",
  "Drakensberg hiking 🥾",
  "Karoo road trip 🚗"
]

export const SA_CHAT_STARTERS = [
  "Howzit! How's your day going?",
  "Eish, this weather is lekker hey!",
  "Sharp! What you up to this weekend?",
  "Anyone planning a braai soon?",
  "How about them Boks? 🏉",
  "Load shedding treating you well?",
  "Traffic was hectic this morning!",
  "Lekker to chat with you again!",
  "Ag man, Monday already?",
  "Just now we should meet up!",
  "Is it? That's hectic bru!",
  "Shame man, hope it gets better!",
  "Jol was lekker last night!",
  "Boet, you must try this place!",
  "China, long time no see!",
  "Tjommie, what's the latest?",
  "Bru, you won't believe this!",
  "Girlie, how's things?",
  "Ou toppie knows best hey!",
  "Tannie makes the best koeksisters!"
]

export const SA_ROOM_CATEGORIES = [
  { id: 'general', name: 'General SA', icon: '🇿🇦', description: 'General South African chat' },
  { id: 'sports', name: 'Sports', icon: '🏉', description: 'Rugby, cricket, soccer and more' },
  { id: 'braai', name: 'Braai & Food', icon: '🍖', description: 'Food, recipes, and braai tips' },
  { id: 'music', name: 'SA Music', icon: '🎵', description: 'Amapiano, kwaito, rock, and more' },
  { id: 'travel', name: 'Travel SA', icon: '🗺️', description: 'Local travel and tourism' },
  { id: 'students', name: 'Students', icon: '🎓', description: 'University and school life' },
  { id: 'work', name: 'Work Life', icon: '💼', description: 'Career and professional chat' },
  { id: 'tech', name: 'Tech SA', icon: '💻', description: 'Technology and innovation' },
  { id: 'culture', name: 'Culture', icon: '🎭', description: 'Arts, heritage, and traditions' },
  { id: 'language', name: 'Languages', icon: '🗣️', description: 'Chat in local languages' },
]

export function getRandomSAGreeting(): string {
  const greetings = [
    "Howzit!", "Sharp!", "Lekker to see you!", "Eish, hello there!",
    "Ag, howzit going?", "Heita!", "Sawubona!", "Molo!", "Dumela!"
  ]
  return greetings[Math.floor(Math.random() * greetings.length)]
}

export function getRandomSAMood(): string {
  return SA_MOOD_SUGGESTIONS[Math.floor(Math.random() * SA_MOOD_SUGGESTIONS.length)]
}

export function getRandomChatStarter(): string {
  return SA_CHAT_STARTERS[Math.floor(Math.random() * SA_CHAT_STARTERS.length)]
}

export function translateSlang(text: string): string {
  let result = text
  Object.entries(SA_SLANG).forEach(([slang, meaning]) => {
    const regex = new RegExp(`\\b${slang}\\b`, 'gi')
    if (regex.test(result)) {
      result = result.replace(regex, `${slang} (${meaning})`)
    }
  })
  return result
}

export function addSAEmojis(text: string): string {
  let result = text
  Object.entries(SA_FOOD_EMOJIS).forEach(([food, emoji]) => {
    const regex = new RegExp(`\\b${food}\\b`, 'gi')
    result = result.replace(regex, `${food} ${emoji}`)
  })
  return result
}
