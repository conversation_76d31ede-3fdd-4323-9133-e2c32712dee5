import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatTime(date: Date): string {
  return new Intl.DateTimeFormat('en-ZA', {
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'Africa/Johannesburg'
  }).format(date)
}

export function formatDate(date: Date): string {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return 'Today'
  } else if (days === 1) {
    return 'Yesterday'
  } else if (days < 7) {
    return new Intl.DateTimeFormat('en-ZA', { weekday: 'long' }).format(date)
  } else {
    return new Intl.DateTimeFormat('en-ZA', { 
      day: 'numeric', 
      month: 'short',
      timeZone: 'Africa/Johannesburg'
    }).format(date)
  }
}

// Mxit-style emoticons mapping
export const mxitEmoticons = {
  ':)': '😊',
  ':-)': '😊',
  ':(': '😢',
  ':-(': '😢',
  ':D': '😃',
  ':-D': '😃',
  ':P': '😛',
  ':-P': '😛',
  ';)': '😉',
  ';-)': '😉',
  ':o': '😮',
  ':-o': '😮',
  ':*': '😘',
  ':-*': '😘',
  '<3': '❤️',
  '</3': '💔',
  ':@': '😠',
  ':-@': '😠',
  '8)': '😎',
  '8-)': '😎',
}

export function convertEmoticons(text: string): string {
  let result = text
  Object.entries(mxitEmoticons).forEach(([emoticon, emoji]) => {
    result = result.replace(new RegExp(escapeRegExp(emoticon), 'g'), emoji)
  })
  return result
}

function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}
